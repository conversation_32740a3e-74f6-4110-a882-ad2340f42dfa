package my.com.mandrill.utilities.feign.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoanPlusErrorResponse implements Serializable {

	private String message;

	private Integer code;

	private List<String> errors = new ArrayList<>();

}
