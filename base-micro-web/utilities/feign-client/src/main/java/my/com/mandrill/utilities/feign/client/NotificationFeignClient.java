package my.com.mandrill.utilities.feign.client;

import my.com.mandrill.utilities.feign.dto.ReminderCheckDTO;
import my.com.mandrill.utilities.feign.dto.ReminderRequest;
import my.com.mandrill.utilities.feign.dto.ReminderResponse;
import my.com.mandrill.utilities.feign.dto.model.PastFutureCount;
import my.com.mandrill.utilities.feign.dto.request.EmailRequest;
import my.com.mandrill.utilities.general.constant.ReminderType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

@FeignClient(name = "notification-component")
public interface NotificationFeignClient {

	@PostMapping("/emails")
	void sendEmail(@RequestBody EmailRequest request, @RequestParam String templateName);

	@PostMapping("/sms")
	void sendSms(Object object, @RequestParam String templateName);

	@DeleteMapping("reminders/clean-up")
	void cleanUp(@RequestParam String userId, @RequestParam List<String> ids, @RequestParam ReminderType reminderType);

	@PostMapping("reminders/integration")
	void integration(@RequestBody ReminderRequest reminderRequest);

	@PutMapping("reminders/integration/{id}")
	ReminderResponse update(@RequestBody ReminderRequest updateReminderRequest, @PathVariable String id);

	@GetMapping("reminders/{reminderType}/{dataId}")
	ReminderResponse findByReminderTypeAndDataId(@PathVariable ReminderType reminderType, @PathVariable String dataId);

	@GetMapping("reminders/{reminderType}/{dataId}")
	ReminderResponse findByReminderTypeAndDataId(@PathVariable ReminderType reminderType, @PathVariable String dataId,
			@RequestParam boolean populateEntity);

	@PostMapping("reminders/integration/check-exist")
	List<ReminderCheckDTO> checkExistReminder(@RequestBody List<ReminderCheckDTO> request);

	@GetMapping("tasks/count/liabilities")
	PastFutureCount countTaskLiabilities(@RequestParam String reminderId, @RequestParam Instant taskDateStart,
			@RequestParam Instant taskDateEnd);

	@DeleteMapping("reminders/{reminderType}/{dataId}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	void deleteByReminderTypeAndDataId(@PathVariable ReminderType reminderType, @PathVariable String dataId);

}
