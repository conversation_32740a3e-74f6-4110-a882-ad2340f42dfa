package my.com.mandrill.utilities.feign.dto.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.ApplicationType;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.RSMStatus;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInterestRecordRSMPaginationDTO {

	private String id;

	private String fullName;

	private String email;

	private String phoneNumber;

	private String nric;

	private String providerId;

	private String providerName;

	private String productCategory;

	private String productType;

	private String productName;

	private Instant createdDate;

	private Instant applicationDate;

	private String refNo;

	private String userRefNo;

	private String source;

	private BigDecimal incomeAmount;

	private ApplicationType applicationType;

	private String applicationTypeName;

	private RSMRelationType rsmRelation;

	private RSMStatus rsmStatus;

	private boolean rsmEligible;

	private boolean rsmCommissionAttached;

	private String balanceTransfer;

	private LocalDate appointmentDate;

	private String showroomAddress;

	private String projectName;

	private String projectAddress;

	private String unitName;

	private BigDecimal cashbackAmount;

	private Long vaultCount;

	private String remarks;

}
