package my.com.mandrill.utilities.feign.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.feign.dto.model.Attachment;
import my.com.mandrill.utilities.feign.dto.model.FileS3ObjectDTO;

import java.io.Serializable;
import java.time.Instant;
import java.util.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRequest implements Serializable {

	private List<String> to = new ArrayList<>();

	private List<String> cc = new ArrayList<>();

	private Set<Attachment> attachments;

	private Set<FileS3ObjectDTO> s3Files;

	@Builder.Default
	private Map<String, Object> templateVariable = new HashMap<>();

	@NotBlank
	private String templateName;

	@NotNull
	private Instant timeTriggered = Instant.now();

	@NotNull
	private Boolean saveToInbox = false;

	private String userId;

}
