package my.com.mandrill.utilities.general.constant;

import lombok.Getter;

@Getter
public enum FinologyHttpCodeEnum {

	FINOLOGY_SUCCESS_CODE("Finology Success Code", "200"),
	FINOLOGY_UNPROCESSABLE_CODE("Finology Unprocessable Code", "422"),
	FINOLOGY_BAD_REQUEST_CODE("Finology Bad Request Code", "400"),
	FINOLOGY_UNAUTHORIZED_CODE("Finology Unauthorized", "401"),

	FINOLOGY_INTERNAL_SERVER_ERROR_CODE("Finology Internal Server Error Code", "500"),

	FINOLOGY_BAD_GATEWAY_CODE("Finology Bad Gateway Code", "502");

	private final String name;

	private final String code;

	FinologyHttpCodeEnum(String name, String code) {
		this.name = name;
		this.code = code;
	}

}
