package my.com.mandrill.utilities.general.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.dto.kafka.KafkaEventWrapper;
import my.com.mandrill.utilities.general.service.KafkaEventWrapperService;
import my.com.mandrill.utilities.general.util.TraceContextHelper;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class KafkaEventWrapperServiceImpl implements KafkaEventWrapperService {

	private final TraceContextHelper traceContextHelper;

	@Override
	public <T> KafkaEventWrapper<T> wrapEvent(T payload) {
		return KafkaEventWrapper.<T>builder().requestId(traceContextHelper.getRequestId()).data(payload).build();
	}

}
