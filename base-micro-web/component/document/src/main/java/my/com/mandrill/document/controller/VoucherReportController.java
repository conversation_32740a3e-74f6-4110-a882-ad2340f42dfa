package my.com.mandrill.document.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.document.dto.model.DocumentOutput;
import my.com.mandrill.document.service.VoucherReportService;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("reports/voucher")
public class VoucherReportController {

	private final VoucherReportService voucherReportService;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_PROPERTY_VOUCHER)")
	public ResponseEntity<StreamingResponseBody> generatePropertyVoucherReport(
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone,
			@RequestParam LocalDate startDate, @RequestParam LocalDate endDate,
			@RequestParam String currentInstitutionId, @RequestParam(required = false) List<String> partnerNames)
			throws IOException {
		ZoneId zoneId = ZoneId.of(timeZone);
		DocumentOutput output = voucherReportService.generatePropertyVoucherReport(currentInstitutionId, startDate,
				endDate, zoneId, partnerNames);
		StreamingResponseBody streamingResponseBody = out -> {
			ByteArrayInputStream inputStream = new ByteArrayInputStream(
					Files.readAllBytes(Paths.get(output.getFile().getAbsolutePath())));
			IOUtils.copy(inputStream, out);
		};
		return ResponseEntity.ok()
				.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + output.getFileName())
				.contentType(output.getDocumentType().getMediaType()).body(streamingResponseBody);
	}

}
