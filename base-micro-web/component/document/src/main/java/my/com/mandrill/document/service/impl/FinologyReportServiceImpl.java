package my.com.mandrill.document.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.document.config.BaseProperties;
import my.com.mandrill.document.dto.model.DocumentOutput;
import my.com.mandrill.document.exception.DocumentComponentException;
import my.com.mandrill.document.exception.ErrorCodeEnum;
import my.com.mandrill.document.service.FinologyReportService;
import my.com.mandrill.utilities.feign.client.FinologyFeignClient;
import my.com.mandrill.utilities.feign.client.PaymentFeignClient;
import my.com.mandrill.utilities.feign.dto.FinologyTransactionDTO;
import my.com.mandrill.utilities.feign.dto.InvoiceDetailsDTO;
import my.com.mandrill.utilities.feign.dto.TransactionDTO;
import my.com.mandrill.utilities.general.constant.DocumentType;
import my.com.mandrill.utilities.general.exception.BusinessException;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class FinologyReportServiceImpl extends JasperReportService implements FinologyReportService {

	public static final String FILE_NAME = "finology-transaction-report.xlsx";

	public static final String DEFAULT_SHEET_NAME = "Product Summary";

	private final BaseProperties baseProperties;

	private final FinologyFeignClient finologyFeignClient;

	private final PaymentFeignClient paymentFeignClient;

	@Override
	public DocumentOutput generateFinologyTransaction(LocalDate startDate, LocalDate endDate) throws IOException {
		List<FinologyTransactionDTO> records = finologyFeignClient.findAllTransactionFinology(startDate, endDate);
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
		String startDateString = startDate.format(dateTimeFormatter);
		String endDateString = endDate.format(dateTimeFormatter);
		String titleDocument = String.format("From %s to %s", startDateString, endDateString);
		return generateFinologyTransaction(records, titleDocument, getDirectory());
	}

	private DocumentOutput generateFinologyTransaction(List<FinologyTransactionDTO> transactions, String titleDocument,
			String directory) {
		DocumentType documentType = DocumentType.XLSX;
		File file = Paths.get(directory, documentType.getRandomFileName()).toFile();
		try {
			Map<String, Object> jasperParam = new HashMap<>();
			jasperParam.put("reportDate", titleDocument);
			jasperParam.put("REPORT_DATA", new JRBeanCollectionDataSource(transactions));

			JasperReport jasperReport = JasperCompileManager.compileReport(finologyTransactionReport.getInputStream());
			List<JasperPrint> prints = new ArrayList<>();

			JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, jasperParam, new JREmptyDataSource());
			prints.add(jasperPrint);

			return new DocumentOutput(exportXlsxReport(file, new String[] { DEFAULT_SHEET_NAME }, prints),
					FinologyReportServiceImpl.FILE_NAME, documentType);

		}
		catch (Exception e) {
			String msg = String.format("Failed to export %s", file.getName());
			log.error(msg, e);
			throw new DocumentComponentException(msg, e);
		}
	}

	@Override
	public String getDirectory() throws IOException {
		Path path = Paths.get(baseProperties.getReport().getPath().getGenerated());
		if (Files.notExists(path)) {
			Files.createDirectories(path);
		}
		return path.toString();
	}

	@Override
	public DocumentOutput generateFinologyOrderDetail(String id) throws IOException {
		TransactionDTO transactionDTO = paymentFeignClient.findById(id);
		if (transactionDTO.getEntityId() == null) {
			throw new BusinessException(ErrorCodeEnum.ENTITY_INCORRECT_FOR_FINOLOGY);
		}
		InvoiceDetailsDTO invoiceDetailsDTO = finologyFeignClient.exportInvoiceDetails(transactionDTO.getEntityId());
		return generateFinologyOrderDetailPDF(invoiceDetailsDTO,
				"Insurance and Road Tax".concat(DocumentType.PDF.getExtension()), getDirectory());
	}

	private DocumentOutput generateFinologyOrderDetailPDF(InvoiceDetailsDTO data, String fileName, String directory)
			throws IOException {
		DocumentType documentType = DocumentType.PDF;
		File file = Paths.get(directory, documentType.getRandomFileName()).toFile();

		URL imageUrl = new URL(data.getIssuerLogo());
		BufferedImage image = ImageIO.read(imageUrl);
		try {
			JasperReport jasperReport = JasperCompileManager.compileReport(finologyOrderDetail.getInputStream());
			JasperReport jasperSubReport = JasperCompileManager.compileReport(finologySubOrderDetail.getInputStream());
			Map<String, Object> jasperParam = new HashMap<>();
			jasperParam.put("subReport", jasperSubReport);
			jasperParam.put("issuerLogo", image);

			List<JasperPrint> prints = new ArrayList<>();

			JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, jasperParam,
					new JRBeanCollectionDataSource(List.of(data)));
			prints.add(jasperPrint);

			return new DocumentOutput(exportPdfReport(file, prints), fileName, documentType);
		}
		catch (Exception e) {
			String msg = String.format("Failed to export %s", file.getName());
			log.error(msg, e);
			throw new DocumentComponentException(msg, e);
		}
	}

}