<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="UserInterestedRecord" pageWidth="1050" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="fccf803e-8058-41a6-9224-3dcbe5c15436" whenNoDataType="AllSectionsNoDetail">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MoneyXLocal"/>
	<parameter name="reportDate" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[]]>
	</queryString>
	<field name="createdDate" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="created_date"/>
		<property name="com.jaspersoft.studio.field.label" value="created_date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<field name="issuerType" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="issuer_type"/>
		<property name="com.jaspersoft.studio.field.label" value="issuer_type"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<field name="provider" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="provider_id"/>
		<property name="com.jaspersoft.studio.field.label" value="provider_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<field name="productType" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="product_type"/>
		<property name="com.jaspersoft.studio.field.label" value="product_type"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<field name="productName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="product_name"/>
		<property name="com.jaspersoft.studio.field.label" value="product_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<field name="num" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.name" value="num"/>
		<property name="com.jaspersoft.studio.field.label" value="num"/>
	</field>
	<field name="fullName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="full_name"/>
		<property name="com.jaspersoft.studio.field.label" value="full_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<field name="phoneNumber" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="phone_number"/>
		<property name="com.jaspersoft.studio.field.label" value="phone_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<field name="email" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="email"/>
		<property name="com.jaspersoft.studio.field.label" value="email"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<field name="userRefNo" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="user_ref_no"/>
		<property name="com.jaspersoft.studio.field.label" value="user_ref_no"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user_interest_record"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="40" splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="30" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="240" height="30" uuid="7f39d011-8afd-4701-917b-2b4a07c0034f"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{reportDate}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="30" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="70" height="30" uuid="f293f368-3032-4180-8c44-5b97b64759e3">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ab0d551a-8253-4a26-a6bf-724d928681a8"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="0" width="70" height="30" uuid="8ec2c358-ecdb-4a91-86a1-fa4325a5bbb5">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5b8cc85b-994b-4638-89bc-201aa46dff8a"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Module]]></text>
			</staticText>
			<staticText>
				<reportElement x="140" y="0" width="100" height="30" uuid="c3d31e89-f7f2-4cb2-95f6-fa64fa79b4d1">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6d83b264-4e8d-4dda-919e-8a12cbafe5ca"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Provider]]></text>
			</staticText>
			<staticText>
				<reportElement x="340" y="0" width="140" height="30" uuid="9eec4fbd-bf0b-4385-8eb9-32600fd6e35e">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c313be2d-dd7a-44e6-a575-304acda4602a"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Product Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="480" y="0" width="100" height="30" uuid="c6f65f4d-2a94-4579-8108-c69980df8512">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7551f76a-0195-4dce-a980-3fb266ae7087"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="580" y="0" width="90" height="30" uuid="8a519b7d-e8ed-4057-8cf4-37bc5772be80">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7551f76a-0195-4dce-a980-3fb266ae7087"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Mobile No.]]></text>
			</staticText>
			<staticText>
				<reportElement x="670" y="0" width="120" height="30" uuid="ad09fc65-9c2b-4f60-88ec-71c7199b1a72">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7551f76a-0195-4dce-a980-3fb266ae7087"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Email Address]]></text>
			</staticText>
			<staticText>
				<reportElement x="790" y="0" width="110" height="30" uuid="8fa1d956-4da7-498b-8c9b-b19606fa0270">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7551f76a-0195-4dce-a980-3fb266ae7087"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[User Reference No]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="0" width="100" height="30" uuid="aefda718-6d10-4717-8bf7-2e7dfa181f48">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6d83b264-4e8d-4dda-919e-8a12cbafe5ca"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Product Type]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="30" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="70" height="30" uuid="3dce3e3d-5d01-4718-ab07-e5277d3db909">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ab0d551a-8253-4a26-a6bf-724d928681a8"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{createdDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="70" y="0" width="70" height="30" uuid="18b31d04-d5d9-4344-8745-345ed295a407">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5b8cc85b-994b-4638-89bc-201aa46dff8a"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{issuerType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="140" y="0" width="100" height="30" uuid="5605a5a0-5fb7-4ce4-bf3f-2d77c1c0a573">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6d83b264-4e8d-4dda-919e-8a12cbafe5ca"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{provider}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="340" y="0" width="140" height="30" uuid="98bd6cd7-d5db-421a-9bba-4747da69f7b4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="fed99f8b-633b-4343-8cab-40d106a71b71"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{productName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="480" y="0" width="100" height="30" uuid="a60dcd4f-d1b1-4ee1-9271-780eaddd9189">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dcc7dc0c-3da8-41ad-af18-************"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{fullName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="580" y="0" width="90" height="30" uuid="a62a1c3a-6128-4597-953b-56d9f35bf6d4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dcc7dc0c-3da8-41ad-af18-************"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{phoneNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="670" y="0" width="120" height="30" uuid="494a6019-f9be-416a-9c08-6250655df6fc">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dcc7dc0c-3da8-41ad-af18-************"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{email}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="790" y="0" width="110" height="30" uuid="bf268dfc-28f2-48a7-aa11-c55b93a627b1"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{userRefNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="240" y="0" width="100" height="30" uuid="3b0a9b60-34e4-43a0-99bd-3a5fc8f5d4b8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6d83b264-4e8d-4dda-919e-8a12cbafe5ca"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textFieldExpression><![CDATA[$F{productType}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="53" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="54" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
