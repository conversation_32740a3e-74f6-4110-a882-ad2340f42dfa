<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.2.final using JasperReports Library version 6.21.2-8434a0bd7c3bbc37cbf916f2968d35e4b165821a  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="purchase-vehicle-statistics-report" pageWidth="1800" pageHeight="842" orientation="Landscape" columnWidth="1210" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="56f6fd57-d1f7-4f87-9b01-765cccc94ef9">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="PURCHASE_VEHICLE_STATISTICS" uuid="7f83bfd8-7d2a-46af-9dad-5d32a1158639">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="date" class="java.lang.String"/>
		<field name="vehicleBrand" class="java.lang.String"/>
		<field name="vehicleModel" class="java.lang.String"/>
		<field name="vehicleVariant" class="java.lang.String"/>
		<field name="totalLike" class="java.lang.Long"/>
		<field name="totalUniqueLike" class="java.lang.Long"/>
		<field name="totalDislike" class="java.lang.Long"/>
		<field name="totalUniqueDislike" class="java.lang.Long"/>
		<field name="totalDetailsClick" class="java.lang.Long"/>
		<field name="totalUniqueDetailsClick" class="java.lang.Long"/>
		<field name="totalCompareClick" class="java.lang.Long"/>
		<field name="totalUniqueCompareClick" class="java.lang.Long"/>
		<field name="totalDownloadBrochure" class="java.lang.Long"/>
		<field name="totalUniqueDownloadBrochure" class="java.lang.Long"/>
		<field name="totalSubmission" class="java.lang.Long"/>
		<field name="totalUniqueSubmission" class="java.lang.Long"/>
	</subDataset>
	<parameter name="dateFrom" class="java.lang.String"/>
	<parameter name="dateTo" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="65">
			<staticText>
				<reportElement x="0" y="0" width="80" height="40" uuid="5a8840e3-cd32-4d63-8d81-b3d60fe12832"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Date Range: ]]></text>
			</staticText>
			<textField>
				<reportElement x="180" y="0" width="100" height="40" uuid="53a2d7cc-126a-45a7-872e-ceb55000b9c3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dateFrom}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="80" y="0" width="100" height="40" uuid="967a0372-16ae-4c97-b598-95cf80c9653d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[From]]></text>
			</staticText>
			<staticText>
				<reportElement x="280" y="0" width="100" height="40" uuid="e6a38f1f-7b18-4045-9129-fe83712d723a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[To]]></text>
			</staticText>
			<textField>
				<reportElement x="380" y="0" width="100" height="40" uuid="ac6b4492-a4dc-4359-af35-d2d9773b3a27"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dateTo}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="225" splitType="Stretch">
			<componentElement>
				<reportElement x="0" y="0" width="1380" height="200" uuid="f17ec9bb-8bd0-447e-bf27-804e25092211">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
					<datasetRun subDataset="PURCHASE_VEHICLE_STATISTICS" uuid="161ccb5e-0b63-418b-9d84-c35ee533f0ce">
						<dataSourceExpression><![CDATA[$P{REPORT_DATA_SOURCE}]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="80" uuid="470fa9af-bc08-4b96-aa28-a28094d41fa9">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="80" height="50" uuid="9170df5a-f4f0-478b-bfca-9270dd3e5166"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Date]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="80" height="30" uuid="5cd56499-6976-4215-b67f-4a56c381d7ff"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{date}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="342e73f8-63b1-41b3-9fda-517f1e5928aa">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="535ce3db-6e79-425d-8901-5ac13081f8e8"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Vehicle Brand]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="80" height="30" uuid="a7debda6-9d8c-4546-84e5-feca6a1e6d9d"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{vehicleBrand}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="0443b3eb-bb82-4e1a-82c6-850d79fe9170">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="890ad07c-f63b-4ed0-8a13-200f71dbba3f">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Vehicle Model]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="80" height="30" uuid="ef58e077-2a90-4292-a3f6-3a34d52dd4d3"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{vehicleModel}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="f2f4404d-cd53-4095-a552-9022b4cdee7b">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="d2e47b11-8c8e-49ad-8f01-43bfa3d1b81a">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Vehicle Variant]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="8191283d-9650-4179-8e6e-82997cb0bd0f"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{vehicleVariant}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="08df46c8-5b16-4dea-8f08-21748e5d784a">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="29a217ce-ce1d-4252-b5c6-14edceef069c"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Like]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="4a87a6c1-2a24-441d-b7cc-f774be3d4517"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalLike}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="8f53b130-3fca-4c8b-8561-f915747ae495">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="3611cfbf-f92c-470f-8f59-b4ffc7ceb698"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Like (Unique User)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="c8f0acf0-608d-4cf1-809a-8b8b34d1f44f"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalUniqueLike}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="730c2d92-2fff-457e-b7cc-9cc01b5c5f53">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column7"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="ee48a8a6-cf32-439f-853f-1bdf17f44817"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Dislike]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="6a4ba471-8009-4b86-8cae-5889ad157586"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalDislike}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column8"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Dislike (Unique User)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalUniqueDislike}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column9"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Details Click]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalDetailsClick}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column10"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Details Click (Unique User)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalUniqueDetailsClick}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column11"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Compare Click]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalCompareClick}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column12"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Compare Click (Unique User)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalUniqueCompareClick}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column13"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Download Brochure]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalDownloadBrochure}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column14"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Download Brochure (Unique User)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalUniqueDownloadBrochure}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column15"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Submission]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalSubmission}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="ec01ce39-9c25-415d-bd4f-9dab7817373e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column16"/>
						<jr:columnHeader style="Table_CH" height="50" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="50" uuid="25d4000b-bf93-421d-93a9-36dffb910fcb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total Number of Submission (Unique User)]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="30">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="100" height="30" uuid="05793435-fadf-491e-b6b7-ac8f24c36f22"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{totalUniqueSubmission}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</detail>
</jasperReport>
