package my.com.mandrill.component.config;

import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

@Configuration
public class KafkaTopicConfig {

	public static final String GROUP = "promotion";

	public static final String ADS_REGISTRATION_CLEANUP = "ads-registration-cleanup";

	public static final String VOUCHER_RESERVE_RELEASE = "voucher-reserve-release";

	public static final String ADVERTISEMENT_END_CHECKER = "advertisement-end-checker";

	@Bean
	public NewTopic adsRegistrationCleanup() {
		return TopicBuilder.name(ADS_REGISTRATION_CLEANUP).partitions(KafkaTopic.LOW_PARTITIONS.getPartitions())
				.replicas(KafkaTopic.LOW_PARTITIONS.getReplicas()).build();
	}

	@Bean
	public NewTopic voucherReserveRelease() {
		return TopicBuilder.name(VOUCHER_RESERVE_RELEASE).partitions(KafkaTopic.LOW_PARTITIONS.getPartitions())
				.replicas(KafkaTopic.LOW_PARTITIONS.getReplicas()).build();
	}

	@Bean
	public NewTopic advertisementEndCheckerTopic() {
		return TopicBuilder.name(ADVERTISEMENT_END_CHECKER).partitions(KafkaTopic.LOW_PARTITIONS.getPartitions())
				.replicas(KafkaTopic.LOW_PARTITIONS.getReplicas()).build();
	}

}
