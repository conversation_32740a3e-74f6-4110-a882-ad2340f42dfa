package my.com.mandrill.component.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode
public class WebCampaignResponse implements Serializable {

	private String id;

	private String campaignName;

	private String title;

	private String subTitle;

	private String imageUrl;

	private Boolean isActive;

	private Boolean isHighlight;

	private String campaignUrl;

	private String slug;

	private String category;

	private String productId;

	private Instant createdDate;

	private Integer sequence;

	private String subCategory;

}
