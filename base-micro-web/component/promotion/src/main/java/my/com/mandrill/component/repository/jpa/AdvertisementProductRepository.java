package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Advertisement;
import my.com.mandrill.component.domain.AdvertisementPredefinedProduct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdvertisementProductRepository extends JpaRepository<AdvertisementPredefinedProduct, String> {

	List<AdvertisementPredefinedProduct> findByIdOrderBySequenceAsc(String id);

	List<AdvertisementPredefinedProduct> findByAdvertisementOrderBySequenceAsc(Advertisement advertisement);

	List<AdvertisementPredefinedProduct> findByProductId(String productId);

}
