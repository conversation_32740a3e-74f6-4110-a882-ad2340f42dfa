package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Advertisement;
import my.com.mandrill.component.domain.UserAdvertisement;
import my.com.mandrill.utilities.general.constant.UserAdvertisementTypeEnum;

import java.util.List;

public interface UserAdvertisementService {

	List<UserAdvertisement> findAllByUserId(String userId);

	UserAdvertisement findById(String id, String userId);

	UserAdvertisement save(UserAdvertisement userAdvertisement);

	UserAdvertisement findFirstByUserIdAndAdvertisementAndUserJourneyIdNotNullOrderByCreatedDateDescOrElseNull(
			String userId, Advertisement advertisement);

	UserAdvertisement findFirstByIsIOSTrueAndIsConsumedByIOSFalseAndTypeAndUniqueIdentifierOrderByCreatedDateDescOrElseNull(
			UserAdvertisementTypeEnum userAdvertisementTypeEnum, String uniqueIdentifier);

	UserAdvertisement findFirstByIsIOSTrueAndIsConsumedByIOSFalseAndTypeAndUniqueIdentifierOrderByCreatedDateDescOrElseThrowRegistrationIOSNotFound(
			UserAdvertisementTypeEnum userAdvertisementTypeEnum, String uniqueIdentifier);

	List<UserAdvertisement> findByIsIOSTrueAndIsConsumedByIOSFalseAndType(
			UserAdvertisementTypeEnum userAdvertisementTypeEnum);

}
