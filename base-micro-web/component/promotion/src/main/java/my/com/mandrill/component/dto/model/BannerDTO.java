package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BannerDTO implements Serializable {

	private String id;

	private String code;

	private String titleEn;

	private String titleMy;

	private String titleCn;

	private String descriptionEn;

	private String descriptionMy;

	private String descriptionCn;

	private Boolean active;

	private String imageEn;

	private String imageMy;

	private String imageCn;

	private Instant startDate;

	private Instant endDate;

}
