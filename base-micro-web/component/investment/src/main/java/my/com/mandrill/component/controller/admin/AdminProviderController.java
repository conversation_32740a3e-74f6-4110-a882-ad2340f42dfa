package my.com.mandrill.component.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.ProviderType;
import my.com.mandrill.component.dto.request.ProviderRequest;
import my.com.mandrill.component.dto.response.ProviderResponse;
import my.com.mandrill.component.service.ProviderIntegrationService;
import my.com.mandrill.component.service.ProviderService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/providers")
@RequiredArgsConstructor
public class AdminProviderController {

	private final ProviderService providerService;

	private final ProviderIntegrationService providerIntegrationService;

	@GetMapping("provider-types")
	@PreAuthorize("hasAuthority(@authorityPermission.INVESTMENT_PROVIDER_READ)")
	public ResponseEntity<List<ProviderType>> getAllProviderType() {
		return ResponseEntity.ok(List.of(ProviderType.values()));
	}

	@GetMapping("/pagination/all")
	@PreAuthorize("hasAuthority(@authorityPermission.INVESTMENT_PROVIDER_READ)")
	public ResponseEntity<Page<ProviderResponse>> getAll(Pageable pageable,
			@RequestParam(required = false) String name) {
		Page<ProviderResponse> result = providerService.findAllPaginated(pageable, name)
				.map(MapStructConverter.MAPPER::toProviderResponse);
		return ResponseEntity.ok(result);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.INVESTMENT_PROVIDER_CREATE)")
	public ResponseEntity<Void> create(@RequestBody @Valid ProviderRequest providerRequest) {
		providerIntegrationService.create(providerRequest);
		return ResponseEntity.ok().build();
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/{id}/active/{status}")
	@PreAuthorize("hasAuthority(@authorityPermission.INVESTMENT_PROVIDER_UPDATE)")
	public void updateProviderStatus(@PathVariable String id, @PathVariable boolean status) {
		providerIntegrationService.updateStatus(id, status);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.INVESTMENT_PROVIDER_UPDATE)")
	public void update(@PathVariable String id, @RequestBody @Valid ProviderRequest providerRequest) {
		providerIntegrationService.update(id, providerRequest);
	}

	@GetMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.INVESTMENT_PROVIDER_READ)")
	public ResponseEntity<ProviderResponse> getById(@PathVariable String id) {
		return ResponseEntity.ok(MapStructConverter.MAPPER.toProviderResponse(providerService.findById(id)));
	}

}
