package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.domain.UserSurvey;
import my.com.mandrill.component.dto.request.SurveyEventRequest;
import my.com.mandrill.component.repository.jpa.UserSurveyRepository;
import my.com.mandrill.component.service.UserSurveyService;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

@Service
@RequiredArgsConstructor
public class UserSurveyServiceImpl implements UserSurveyService {

	private final UserSurveyRepository userSurveyRepository;

	@Override
	public void createOrUpdateUserSurvey(SurveyEventRequest request) {
		UserSurvey userSurvey = userSurveyRepository
				.findByUserIdAndSurveyType(request.getUserId(), request.getFormType()).orElseGet(() -> {
					UserSurvey newSurvey = new UserSurvey();
					newSurvey.setUserId(request.getUserId());
					newSurvey.setSurveyType(request.getFormType());
					return newSurvey;
				});

		userSurvey.setNextSurveyDate(Instant.now().plus(90, ChronoUnit.DAYS));
		userSurvey.setAction(request.getAction());
		userSurveyRepository.save(userSurvey);
	}

	@Override
	public UserSurvey getUserSurvey(String id) {
		return userSurveyRepository.findById(id).orElse(null);
	}

}
