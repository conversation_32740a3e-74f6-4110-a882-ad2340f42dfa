package my.com.mandrill.component.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Merchant;
import my.com.mandrill.component.dto.model.MerchantDTO;
import my.com.mandrill.component.dto.request.MerchantRequest;
import my.com.mandrill.component.service.MerchantService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("admin/merchants")
public class AdminMerchantController {

	private final MerchantService merchantService;

	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.PAYMENT_MERCHANT_UPDATE)")
	public ResponseEntity<MerchantDTO> update(@PathVariable String id,
			@Valid @RequestBody MerchantRequest merchantRequest) {
		Merchant merchant = merchantService.update(id, merchantRequest);
		merchant = merchantService.save(merchant);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toMerchantDTO(merchant));
	}

}
