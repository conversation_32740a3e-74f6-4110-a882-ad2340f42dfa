package my.com.mandrill.component.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.servlet.http.HttpServletRequest;
import my.com.mandrill.component.domain.Transaction;
import my.com.mandrill.component.dto.model.TransactionHistoryDTO;
import my.com.mandrill.component.dto.response.GKashResponse;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.InvoiceDetailsDTO;
import my.com.mandrill.utilities.feign.dto.TransactionDTO;
import my.com.mandrill.utilities.general.constant.EntityName;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface TransactionIntegrationService {

	Transaction createUsingDefaultMerchant(Transaction transaction, CurrentUserIdDTO userDTO, String remoteAddr);

	Transaction create(Transaction transaction, CurrentUserIdDTO userDTO, String remoteAddr);

	Transaction recurring(String transactionId, String cardId, String userId, HttpServletRequest request)
			throws JsonProcessingException;

	Transaction callback(Transaction transaction, GKashResponse gKashResponse, HttpServletRequest request);

	Transaction expire(String transactionId, String userId);

	Transaction refund(String transactionId, String userId, HttpServletRequest request);

	Transaction query(Transaction transaction, HttpServletRequest request);

	void postCallback(Transaction transaction) throws JsonProcessingException;

	Page<TransactionHistoryDTO> findHistoryByEntityName(String userId, EntityName entityName, Pageable pageable);

}
