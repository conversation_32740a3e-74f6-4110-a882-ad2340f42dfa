package my.com.mandrill.component.config;

import my.com.mandrill.component.client.GKashClient;
import my.com.mandrill.utilities.general.util.ConnectionUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

@Configuration
@ConditionalOnProperty(name = "base.payment-gateway.g-kash.enabled", havingValue = "true")
public class GKashClientConfig {

	private final BaseProperties baseProperties;

	public GKashClientConfig(BaseProperties baseProperties) {
		this.baseProperties = baseProperties;
	}

	@Bean
	public WebClient gKashWebClient() {
		return WebClient.builder().baseUrl(baseProperties.getPaymentGateway().getGKash().getServerUrl())
				.clientConnector(new ReactorClientHttpConnector(
						HttpClient.create(ConnectionUtil.getDefaultConnectionProvider())))
				.build();
	}

	@Bean
	public GKashClient gKashClient(WebClient gKashWebClient) {
		HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
				.builder(WebClientAdapter.forClient(gKashWebClient))
				.blockTimeout(Duration.parse(baseProperties.getPaymentGateway().getGKash().getBlockingTimeout()))
				.build();
		return httpServiceProxyFactory.createClient(GKashClient.class);
	}

}
