package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import my.com.mandrill.utilities.general.constant.RecurringType;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecurringCardDTO implements Serializable {

	private String id;

	private String cardDetail;

	private RecurringType recurringType;

}