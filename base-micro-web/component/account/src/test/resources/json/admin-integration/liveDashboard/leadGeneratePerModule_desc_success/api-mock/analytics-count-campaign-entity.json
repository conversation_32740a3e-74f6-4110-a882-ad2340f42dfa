{"request": {"method": "GET", "urlPath": "/user-interested-redirects/integration/count-campaign-entity"}, "response": {"body": "[\n  { \"entityName\": \"VEHICLE\", \"count\": 324 },\n  { \"entityName\": \"PROPERTY\", \"count\": 877 },\n  { \"entityName\": \"PROPERTY_STAGING\", \"count\": 529 },\n  { \"entityName\": \"UTILITY\", \"count\": 612 },\n  { \"entityName\": \"BANK\", \"count\": 92 },\n  { \"entityName\": \"CREDIT_CARD\", \"count\": 410 },\n  { \"entityName\": \"INSURANCE\", \"count\": 674 },\n  { \"entityName\": \"LOAN\", \"count\": 305 },\n  { \"entityName\": \"BANKLIST\", \"count\": 781 },\n  { \"entityName\": \"USER\", \"count\": 159 },\n  { \"entityName\": \"LEGAL\", \"count\": 241 },\n  { \"entityName\": \"GET_TO_KNOW_CC\", \"count\": 663 },\n  { \"entityName\": \"ADVERTISEMENT\", \"count\": 495 },\n  { \"entityName\": \"<PERSON>N<PERSON>_YOUR_LOAN_LIMIT\", \"count\": 812 },\n  { \"entityName\": \"LOAN_LIMIT\", \"count\": 137 },\n  { \"entityName\": \"BANNER\", \"count\": 954 },\n  { \"entityName\": \"FINOLOGY_VEHICLE_INSURANCE\", \"count\": 321 },\n  { \"entityName\": \"M_AND_A_INTEREST\", \"count\": 753 },\n  { \"entityName\": \"SOLAROO\", \"count\": 101 },\n  { \"entityName\": \"EWILL\", \"count\": 258 },\n  { \"entityName\": \"STASHAWAY\", \"count\": 821 },\n  { \"entityName\": \"AHAM_CAPITAL\", \"count\": 670 },\n  { \"entityName\": \"JOMHIBAH\", \"count\": 487 },\n  { \"entityName\": \"SINEGY_DAX\", \"count\": 369 },\n  { \"entityName\": \"CREDIT_BUREAU\", \"count\": 444 },\n  { \"entityName\": \"EXPENSE\", \"count\": 600 },\n  { \"entityName\": \"EASIWILL\", \"count\": 287 },\n  { \"entityName\": \"CTOS\", \"count\": 956 },\n  { \"entityName\": \"INCOME\", \"count\": 135 },\n  { \"entityName\": \"VAULT\", \"count\": 790 },\n  { \"entityName\": \"E_ACCESS\", \"count\": 213 },\n  { \"entityName\": \"ATX\", \"count\": 684 },\n  { \"entityName\": \"MILIEU_SOLAR\", \"count\": 511 },\n  { \"entityName\": \"PAYMENT\", \"count\": 329 },\n  { \"entityName\": \"MODULE\", \"count\": 928 },\n  { \"entityName\": \"REDIRECT\", \"count\": 580 },\n  { \"entityName\": \"CGS_INT_MY\", \"count\": 309 },\n  { \"entityName\": \"INVESTMENT\", \"count\": 732 },\n  { \"entityName\": \"SAVING_GOAL\", \"count\": 861 },\n  { \"entityName\": \"DWS\", \"count\": 146 }\n]\n", "headers": {"Content-Type": "application/json"}, "status": 200}}