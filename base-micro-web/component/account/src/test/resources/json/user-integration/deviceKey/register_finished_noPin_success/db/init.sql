INSERT INTO account.app_user
(id,
 created_date,
 created_by,
 referral_code,
 last_modified_date,
 last_modified_by,
 ref_no,
 username,
 password_hash,
 provider,
 full_name,
 email,
 email_verified,
 phone_country,
 phone_number,
 phone_verified,
 lang_key, active, login_fail_attempt, login_type, nric, address1, address2, address3, postcode, country_id, state_id, nationality_id, gender, marital_status, ethnicity, religion, currency_id, epf_contribution, socso, eis, education_level_id, employment_type_id, occupation_group_id, business_nature_id, self_employed_name, segment_id, age, secret_key, deleted_datetime, deleted, term_condition_version, privacy_policy_version, platform_agreement_version, passport, army, dob, is_nric_editable, is_nationality_editable, pin)
VALUES('256934b7-0d8f-419b-b174-fc1c41da0054',
       '2024-11-14 00:50:53.035',
       '*************',
       null,
       '2024-11-14 02:20:11.809',
       '*************',
       '*************',
       '*************',
       '$2a$10$qwgYg1N/3Q8XeNb7rPxOLuQx3bImw4Bve7QNOovG5KTRxVJcryVW6',
       'DATABASE',
       'Device Key Integration Test',
       'EvHR7uMjYBLd2RLXmyacMh8vQKpXsLkNIoKStC18cyc6gAc=',
       true,
       '+60',
       'R6WVtbN1PkKQg7h7PvB5veIqJdbNXtYh',
       true,
       'en', true, 0, 'USER', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0a1334c0-ada3-11ed-a899-0242ac120002', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ea035370-ada2-11ed-a899-0242ac120002', NULL, NULL, NULL, NULL, NULL, 'MuCQL5vqrCo2nzBo9fMl8R8Jw8BiQa9k1S4Qs4LHXs0GMt2E/iPWgGEJwBvxshR+WDUDTcuy9WhnVWc++tA3cZOfQ1Et+aRj', NULL, false, NULL, NULL, NULL, NULL, NULL, 'BalHY2z+Ok0P9Z/o+b/CuFS9EvM7d0teodaXAU9gQjBFtki6+24=', true, true, NULL);

INSERT INTO account.app_user_institution
(institution_id, user_id)
VALUES('97a0a02a-02ef-4616-96db-a1abf22a8b40', '256934b7-0d8f-419b-b174-fc1c41da0054');

INSERT INTO account.app_user_authority
(authority_id, user_id)
VALUES('469e536a-cd4e-4caa-bd6b-67a46356d3f5', '256934b7-0d8f-419b-b174-fc1c41da0054');

INSERT INTO account.app_user_key_request
(id, created_date, created_by, last_modified_date, last_modified_by, username, key_value, "type", initial_date, status, completion_date, attempt, fail_verification_attempt, expires_at)
VALUES('f50cb6af-a975-482d-a07e-ab47dfa70822', '2023-03-06 17:53:28.643', 'anonymous', '2023-06-25 04:37:34.162', '*************', 'XaKXtrF0OUSXhVOjxOuuZ0ovsurdrbL5I34e', 'EvHR7uMjYBLd2RLIxnvMZ0aEH8YQR4zMUk6kto624A==', 'VERIFICATION_ENABLE_BIOMETRIC', '2023-03-07 01:53:28.643', 'PENDING', '2023-03-06 17:55:17.759', 0, 0, '9999-03-07 01:53:28.643');

INSERT INTO account.device_key
(id, created_date, created_by, last_modified_date, last_modified_by, user_id, device_id, device_model, public_key)
VALUES('0fec9816-0968-4b9e-a2c2-fab3ea850d00', '2025-03-27 04:08:13.320', '*************', '2025-03-27 04:08:15.585', '*************', '256934b7-0d8f-419b-b174-fc1c41da0054', 'deviceKeyIntegrationTest', 'SM-A546E', '6ac2MFfPXv0NX1lBM7aTOIGk99xOGxtzHMpKUONXMTk=');

INSERT INTO account.signature_challenge
(id, created_date, created_by, last_modified_date, last_modified_by, value, signature, device_key_id, verified, expired)
VALUES('3e00cce6-1355-4a79-8605-97f285745ec2', '2025-03-27 04:08:13.611', '*************', '2025-03-27 04:08:15.688', '*************', 'integration-test-message', 'Ba3Hf8qVHTbS+HbHXTSVMF+COHm9Wo84x/HnsnihWeYVxr3ZJ+F3gSF+tRts78/BcAzPril+LvHoNY/qvfvSCQ==', '0fec9816-0968-4b9e-a2c2-fab3ea850d00', false, false);