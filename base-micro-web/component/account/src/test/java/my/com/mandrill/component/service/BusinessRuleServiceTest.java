package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Expense;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.Segment;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.service.impl.BusinessRuleServiceImpl;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@Disabled
@ExtendWith(MockitoExtension.class)
class BusinessRuleServiceTest {

	@InjectMocks
	BusinessRuleServiceImpl businessRuleService;

	@InjectMocks
	AesCryptoUtil aesCryptoUtil;

	User mockUser;

	Segment mockSegment;

	Set<Income> incomeSet;

	Set<Expense> expenseSet;

	Income incomeMock;

	Expense expenseMock;

	@BeforeEach
	void setup() {
		ReflectionTestUtils.setField(aesCryptoUtil, "AES_KEY", "NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc");
		ReflectionTestUtils.setField(aesCryptoUtil, "IV_KEY", "57ad289f-3def-4c");

		expenseMock = new Expense();
		expenseSet = new HashSet<>();
		expenseMock.setAmount(new BigDecimal(1000));
		expenseSet.add(expenseMock);
		incomeMock = new Income();
		incomeMock.setMonthlyIncomeAmount(new BigDecimal(10000));
		incomeSet = new HashSet<>();
		incomeSet.add(incomeMock);
		mockUser = new User();
		mockUser.setIncomes(incomeSet);
		mockUser.setExpenses(expenseSet);
		mockSegment = new Segment();
		mockSegment.setCode("0");
		mockUser.setSegment(mockSegment);
	}

	@Test
	void checkImbalanceIncomeExpense() {

		Boolean result = businessRuleService.checkImbalanceIncomeExpense(mockUser);
		assertThat(result).isFalse();
	}

}
