package my.com.mandrill.component.integration.admin;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.dto.model.ObjectDTO;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.utilities.general.constant.Constant;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

public class PrivateInstitutionIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Test
	void getProvider_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/v1/private/institution").queryParam("providerIds", Constant.DEFAULT_INSTITUTION_ID)
						.header("x-internal-api-key", "xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ")
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		List<ObjectDTO> response = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(response);

		List<ObjectDTO> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertEquals(expected.get(0).getId(), response.get(0).getId());
		Assertions.assertEquals(expected.get(0).getName(), response.get(0).getName());

	}

}
