<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="account-component_20240222_loongyeat_0002" author="loongyeat">
        <createTable tableName="weekly_user_target">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_weekly_user_target"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="year" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="week_number" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="target" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="year, week_number" constraintName="uc_37dc960b6b3fedd659e41d0ca" tableName="weekly_user_target"/>
    </changeSet>
</databaseChangeLog>
