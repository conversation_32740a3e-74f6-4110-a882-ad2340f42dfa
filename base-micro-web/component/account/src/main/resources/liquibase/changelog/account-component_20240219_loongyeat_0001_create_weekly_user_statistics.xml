<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="account-component_20240219_loongyeat_0001" author="loongyeat">
        <createTable tableName="weekly_user_statistics_transaction">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_weekly_user_statistics_transaction"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="week" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="week, year, type" constraintName="uc_c830b7ada49b35101e85c618d" tableName="weekly_user_statistics_transaction"/>
    </changeSet>
</databaseChangeLog>
