<?xml version="1.0" encoding="utf-8" ?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <changeSet id="account-component_20230914_wuikeat_0001" author="wuikeat">
        <createTable tableName="epf_contribution">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_epf_contribution"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="contribution" type="VARCHAR(100)"/>
            <column name="month" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="user_id, month, year" constraintName="uc_a231a62e42650a9ae32248e08" tableName="epf_contribution"/>
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="epf_contribution" constraintName="FK_EPF_CONTRIBUTION_ON_USER" referencedColumnNames="id" referencedTableName="app_user"/>
    </changeSet>
</databaseChangeLog>