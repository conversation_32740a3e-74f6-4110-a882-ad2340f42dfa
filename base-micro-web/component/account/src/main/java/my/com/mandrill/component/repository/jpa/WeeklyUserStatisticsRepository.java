package my.com.mandrill.component.repository.jpa;

import lombok.NonNull;
import my.com.mandrill.component.constant.UserStatisticType;
import my.com.mandrill.component.domain.WeeklyUserStatistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.Year;
import java.util.Optional;

@Repository
public interface WeeklyUserStatisticsRepository extends JpaRepository<WeeklyUserStatistics, String> {

	Optional<WeeklyUserStatistics> findByWeekAndYearAndType(int week, @NonNull Year year,
			@NonNull UserStatisticType type);

}
