package my.com.mandrill.component.controller.admin;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.BusinessNature;
import my.com.mandrill.component.dto.model.BusinessNatureDTO;
import my.com.mandrill.component.dto.request.BusinessNatureRequest;
import my.com.mandrill.component.service.BusinessNatureService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Tag(name = "20-business-nature")
@Slf4j
@RestController
@RequestMapping("/admin/business-nature")
@RequiredArgsConstructor
public class AdminBusinessNatureController {

	private final BusinessNatureService businessNatureService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.BUSINESS_NATURE_CREATE)")
	public ResponseEntity<BusinessNatureDTO> createValidation(
			@Valid @RequestBody BusinessNatureRequest businessNatureRequest) {

		BusinessNature businessNature = MapStructConverter.MAPPER.toBusinessNature(businessNatureRequest);

		BusinessNature businessNatureResult = this.businessNatureService.createValidation(businessNature);

		businessNatureResult = this.businessNatureService.save(businessNatureResult);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toBusinessNatureDTO(businessNatureResult));

	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.BUSINESS_NATURE_READ)")
	public ResponseEntity<Page<BusinessNatureDTO>> findAllBusinessNature(Pageable pageable,
			@RequestParam(required = false) String name) {

		Page<BusinessNature> businessNaturePage = this.businessNatureService.findAll(pageable, name);

		return ResponseEntity.ok(businessNaturePage.map(MapStructConverter.MAPPER::toBusinessNatureDTO));

	}

	@GetMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.BUSINESS_NATURE_READ)")
	public ResponseEntity<BusinessNatureDTO> findBusinessNatureById(@PathVariable String id) {

		BusinessNature businessNature = this.businessNatureService.findById(id);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toBusinessNatureDTO(businessNature));

	}

	@PutMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.BUSINESS_NATURE_UPDATE)")
	public ResponseEntity<BusinessNatureDTO> updateValidation(
			@Valid @RequestBody BusinessNatureRequest businessNatureRequest, @PathVariable String id) {

		BusinessNature businessNature = MapStructConverter.MAPPER.toBusinessNature(businessNatureRequest);
		businessNature.setId(id);

		BusinessNature businessNatureResult = this.businessNatureService.updateValidation(businessNature);

		businessNatureResult = this.businessNatureService.save(businessNatureResult);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toBusinessNatureDTO(businessNatureResult));

	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.BUSINESS_NATURE_DELETE)")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public void deleteById(@PathVariable String id) {

		this.businessNatureService.deleteById(id);

	}

}
