package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Segment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SegmentRepository extends JpaRepository<Segment, String> {

	Optional<Segment> findById(String id);

	@Query("SELECT s FROM Segment s WHERE LOWER(s.name) LIKE %:name% ")
	Page<Segment> findAllByName(Pageable pageable, String name);

	boolean existsByCodeIgnoreCaseAndNameIgnoreCase(String code, String name);

}
