package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.response.NetWorthResponse;
import my.com.mandrill.component.service.UserNetWorthTransactionIntgService;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "net-worth")
@Slf4j
@RestController
@RequestMapping("/net-worth")
@RequiredArgsConstructor
public class NetWorthController {

	private final UserNetWorthTransactionIntgService userNetWorthTransactionIntgService;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<NetWorthResponse> getUserNetWorth() {
		return ResponseEntity.ok(userNetWorthTransactionIntgService.getNetWorthByUserId(SecurityUtil.currentUserId()));
	}

}
