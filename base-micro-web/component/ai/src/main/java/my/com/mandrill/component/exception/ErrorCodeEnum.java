package my.com.mandrill.component.exception;

import lombok.Getter;
import my.com.mandrill.utilities.general.exception.ExceptionEnum;

@Getter
public enum ErrorCodeEnum implements ExceptionEnum {

	UNKNOWN_EXCEPTION("AI9999", "This is unexpected error. Contact <EMAIL>"),

	AI_INTEGRATION_FAILED_ERROR("AI0001", "This is unexpected error. Contact <EMAIL>"),
	AI_INTEGRATION_EXCEPTION("AI0002", "This is unexpected error. Contact <EMAIL>"),
	ISSUER_TYPE_INVALID("AI0003", "This is unexpected error. Contact <EMAIL>"),
	NOT_SUPPORTED("AI0004", "This is unexpected error. Contact <EMAIL>"),
	INVALID_PRODUCT_PLATFORM_CODE("AI0005", "This is unexpected error. Contact <EMAIL>"),
	INVALID_PRODUCT_GROUP_CODE("AI0006", "This is unexpected error. Contact <EMAIL>"),
	VEHICLE_APPLICATION_EXISTS("AI0007", "The vehicle application exist"),
	INVALID_FILE_NAME("AI0008", "Invalid file name"), PDF_CONVERSION_ERROR("AI0009", "PDF conversion error"),;

	private final String code;

	private final String description;

	ErrorCodeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

}