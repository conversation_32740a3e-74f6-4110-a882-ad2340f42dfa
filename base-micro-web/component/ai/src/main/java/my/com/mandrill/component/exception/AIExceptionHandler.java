package my.com.mandrill.component.exception;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.exception.ApiError;
import my.com.mandrill.utilities.general.exception.GlobalExceptionHandler;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class AIExceptionHandler extends ResponseEntityExceptionHandler {

	@Override
	@NonNull
	protected ResponseEntity<Object> handleMethodArgumentNotValid(@NonNull MethodArgumentNotValidException ex,
			@NonNull HttpHeaders headers, @NonNull HttpStatusCode status, @NonNull WebRequest request) {
		return GlobalExceptionHandler.handleMethodDefault(ex, headers, status, request);
	}

	@ExceptionHandler(NotSupportedException.class)
	protected ResponseEntity<ApiError> handleNotSupportedException(NotSupportedException ex) {
		return buildResponseEntity(ex.getApiError());
	}

	@ExceptionHandler(AIIntegrationException.class)
	protected ResponseEntity<ApiError> handleAIIntegrationException(AIIntegrationException ex) {
		return buildResponseEntity(ex.getApiError());
	}

	private ResponseEntity<ApiError> buildResponseEntity(ApiError apiError) {
		log.error("AIExceptionHandler");
		log.error("Code: " + apiError.getErrorCode());
		log.error("Status: " + apiError.getStatus().toString());
		log.error("Message: " + apiError.getMessage());
		log.error("Debug Message: " + apiError.getDebugMessage());
		return new ResponseEntity<>(apiError, apiError.getStatus());
	}

}
