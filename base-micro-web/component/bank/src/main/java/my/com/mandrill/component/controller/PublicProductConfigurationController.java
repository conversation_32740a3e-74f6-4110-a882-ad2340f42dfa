package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.ProductConfigurationTemplateField;
import my.com.mandrill.component.domain.ProductConfiguration;
import my.com.mandrill.component.dto.model.ProductConfigurationTemplateDTO;
import my.com.mandrill.component.service.ProductConfigurationService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("v1/public/product-configuration")
public class PublicProductConfigurationController {

	private final ProductConfigurationService productConfigurationService;

	@PublicAuth
	@SecurityRequirements
	@GetMapping("{productId}")
	public List<ProductConfigurationTemplateDTO> store(@PathVariable String productId) {
		List<ProductConfiguration> productConfiguration = productConfigurationService
				.getProductConfigurationByProductId(productId, List.of(ProductConfigurationTemplateField.FILE));

		return productConfiguration.stream().map(v -> {
			if (Objects.nonNull(v.getTemplate())) {
				return MapStructConverter.MAPPER.toProductConfigTemplateDTO(v.getTemplate());
			}
			return null;
		}).filter(Objects::nonNull).toList();
	}

}
