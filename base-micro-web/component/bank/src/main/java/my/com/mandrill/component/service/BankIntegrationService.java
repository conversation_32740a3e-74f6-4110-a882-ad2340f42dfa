package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.dto.model.BankDTO;
import my.com.mandrill.component.dto.request.BankRequest;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import my.com.mandrill.utilities.feign.dto.VaultLinkDTO;
import my.com.mandrill.utilities.general.constant.AccountType;
import org.springframework.data.domain.Sort;

import java.time.Instant;
import java.util.List;

public interface BankIntegrationService {

	Bank process(BankRequest request, String userId, String id);

	void createOrUpdateReminder(BankDetail bankDetail);

	void deleteCreditCard(String id, String userId);

	void completeUserJourney(String dataId, String userJourneyId);

	void delete(String id, String userId);

	void sendDashboardActivity(final AccountType accountType, Instant createdDate);

	void linkVault(BankDetail bankDetail, VaultLinkDTO vaultLinkDTO);

	List<DetailedNetWorthDTO> calculateDetailedNetWorthByUserId(String userId);

	List<BankDTO> findUserBankDetails(String userId, AccountType accountType, String bankListId, Sort sort);

	void deleteBankReminder(String id);

	Bank findByIdAndUserId(String id, String userId);

	void deleteSavingGoalAccount(BankDetail bankDetail);

}
