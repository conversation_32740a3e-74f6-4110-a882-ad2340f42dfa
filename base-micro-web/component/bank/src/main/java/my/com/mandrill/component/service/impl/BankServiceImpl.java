package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.BankRepository;
import my.com.mandrill.component.service.BankDetailService;
import my.com.mandrill.component.service.BankService;
import my.com.mandrill.utilities.general.constant.AccountType;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class BankServiceImpl implements BankService {

	private final BankRepository bankRepository;

	private final BankDetailService bankDetailService;

	@Override
	public Bank findById(String id) {
		return bankRepository.findById(id).orElseThrow(ExceptionPredicate.bankNotFound(id));
	}

	@Override
	public Bank findByIdAndUserId(String id, String userId) {
		return bankRepository.findByIdAndUserId(id, userId).orElseThrow(ExceptionPredicate.bankNotFound(id));
	}

	@Override
	public List<Bank> findByUserId(String userId, Sort sort) {
		return bankRepository.findByUserId(userId, sort);
	}

	@Transactional
	@Override
	public Bank save(Bank bank) {
		return bankRepository.save(bank);
	}

	@Transactional
	@Override
	public Bank saveAndFlush(Bank bank) {
		return bankRepository.saveAndFlush(bank);
	}

	@Override
	public Long count(EntityName entityName, String userId) {
		switch (entityName) {
			case BANK -> {
				return bankDetailService.countByBankUserIdAndAccountTypeIn(userId,
						List.of(AccountType.SAVINGS_ACCOUNT, AccountType.FIXED_DEPOSIT));
			}
			case CREDIT_CARD -> {
				return bankDetailService.countByBankUserIdAndAccountTypeIn(userId, List.of(AccountType.CREDIT_CARD));
			}
			default -> throw ExceptionPredicate.notSupportedByEntityName(entityName.getName()).get();
		}
	}

	@Override
	public boolean existsByBankListIdAndUserId(String bankListId, String userId) {
		return bankRepository.existsByBankListIdAndUserId(bankListId, userId);
	}

	@Override
	@Transactional
	public void delete(Bank bank) {
		bankRepository.delete(bank);
	}

	@Override
	public Optional<Bank> findByBankListIdAndUserId(String bankListId, String userId) {
		return bankRepository.findByBankListIdAndUserId(bankListId, userId);
	}

	@Override
	public List<Bank> findByUserIdAndBankDetailsAccountTypeAndBankListId(String userId, AccountType accountType,
			String bankListId, Sort sort) {
		return bankRepository.findByUserIdAndBankDetailsAccountTypeAndBankListId(userId, accountType, bankListId, sort);
	}

	@Override
	public List<Bank> findByUserIdAndBankDetailsAccountType(String userId, AccountType accountType, Sort sort) {
		return bankRepository.findByUserIdAndBankDetailsAccountType(userId, accountType, sort);
	}

	@Override
	public List<Bank> findByUserIdAndBankListId(String userId, String bankListId, Sort sort) {
		return bankRepository.findByUserIdAndBankListId(userId, bankListId, sort);
	}

	@Override
	@Transactional
	public void deleteByIdAndUserIdAndBankDetailsEmptyAndLoansEmpty(String id, String userId) {
		bankRepository.deleteByIdAndUserIdAndBankDetailsEmptyAndLoansEmpty(id, userId);
	}

	@Override
	@Transactional
	public List<BankDetail> deleteBankDetailByCreditCard(String id, String userId) {
		Bank bank = findByIdAndUserId(id, userId);
		List<BankDetail> deletedBankDetails = bank.getBankDetails().stream()
				.filter(bankDetail -> bankDetail.getAccountType().equals(AccountType.CREDIT_CARD)).toList();

		// there is no credit card
		if (deletedBankDetails.isEmpty()) {
			throw new BusinessException(ErrorCodeEnum.CREDIT_CARD_NOT_EXIST);
		}

		// delete all credit card
		// need to remove both relation
		for (BankDetail detail : deletedBankDetails) {
			bank.getBankDetails().remove(detail);
			bankDetailService.delete(detail);
		}

		return deletedBankDetails;
	}

	@Override
	@Transactional
	public List<BankDetail> deleteAllBankDetail(String id, String userId) {
		Bank bank = findByIdAndUserId(id, userId);

		if (!bank.getLoans().isEmpty()) {
			throw new BusinessException(ErrorCodeEnum.INVALID_DELETE_BANK_WITH_LOAN_ONLY);
		}

		final List<BankDetail> deletedBankDetails = bank.getBankDetails().stream().toList();

		// delete all credit card
		// need to remove both relation
		for (BankDetail detail : deletedBankDetails) {
			bank.getBankDetails().remove(detail);
			bankDetailService.delete(detail);
		}

		return deletedBankDetails;
	}

	/**
	 * This function is here is because bankDetail is cascade all in bank
	 */
	@Override
	@Transactional
	public void deleteBankDetail(BankDetail bankDetail) {
		Bank bank = findByIdAndUserId(bankDetail.getBank().getId(), bankDetail.getBank().getUserId());
		bank.getBankDetails().remove(bankDetail);
		bankDetailService.delete(bankDetail);
	}

}
