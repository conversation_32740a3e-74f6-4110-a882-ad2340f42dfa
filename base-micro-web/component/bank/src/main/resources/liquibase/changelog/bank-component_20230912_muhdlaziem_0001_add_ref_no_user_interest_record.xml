<?xml version="1.0" encoding="utf-8" ?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <changeSet id="bank-component_20230912_muhdlaziem_0001" author="muhdlaziem">
        <dropTable tableName="user_interest_record"/>
        <createTable tableName="user_interest_record">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_user_interest_record"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="ref_no" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="full_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="phone_number" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="provider_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="issuer_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="issuer_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="product_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="product_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="product_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="ref_no" constraintName="uc_c0324f1853158fe81c5ce60cc"
                             tableName="user_interest_record"/>

        <createTable tableName="running_number">
            <column name="module_name" type="VARCHAR(100)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_running_number"/>
            </column>
            <column name="running_number" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="prefix" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="%d" name="number_format" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="include_day" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="institution_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="module_name, institution_id" constraintName="uc_beb280372e3203dd5dbc5fb22"
                             tableName="running_number"/>

        <loadData encoding="UTF-8"
                  file="/liquibase/dataset/running-number/running-number.csv"
                  separator=";"
                  tableName="running_number"/>

    </changeSet>
</databaseChangeLog>