<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.19.xsd">

    <changeSet id="bank-component_20240928_weishun_0001" author="weishun">
        <createTable tableName="loan_eligibility_lender_product">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loan_eligibility_lender_product"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="loan_eligibility_lender_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="product_id" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(255)"/>
            <column name="interest_rate" type="DECIMAL(5, 2)"/>
            <column name="margin_of_finance" type="DECIMAL(5, 2)"/>
            <column name="tenure" type="INT"/>
            <column name="max_loan_amount" type="VARCHAR(255)"/>
            <column name="max_new_monthly_commitment" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="loan_eligibility_lender_id, product_id" constraintName="uc_f70877b51202b22425fdfce8f" tableName="loan_eligibility_lender_product"/>
        <addForeignKeyConstraint baseColumnNames="loan_eligibility_lender_id" baseTableName="loan_eligibility_lender_product" constraintName="FK_LOAN_ELIGIBILITY_LENDER_PRODUCT_ON_LOAN_ELIGIBILITY_LENDER" referencedColumnNames="id" referencedTableName="loan_eligibility_lender"/>
    </changeSet>
</databaseChangeLog>