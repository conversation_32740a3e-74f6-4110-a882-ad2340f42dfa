package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankList;
import my.com.mandrill.component.domain.CreditCardType;
import my.com.mandrill.component.repository.jpa.BankListRepository;
import my.com.mandrill.component.repository.jpa.CreditCardTypeRepository;
import my.com.mandrill.component.service.impl.DataServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@ExtendWith(MockitoExtension.class)
class DataServiceTest {

	@InjectMocks
	DataServiceImpl dataService;

	@Mock
	BankListRepository bankListRepository;

	@Mock
	CreditCardTypeRepository cardTypeRepository;

	Bank bankMock;

	BankList bankListMock;

	CreditCardType creditCardTypeMock;

	List<BankList> bankListListMock;

	List<CreditCardType> creditCardTypeListMock;

	@BeforeEach
	void setup() {
		bankMock = new Bank();
		bankListMock = new BankList();
		creditCardTypeMock = new CreditCardType();

		bankListListMock = new ArrayList<>();
		bankListListMock.add(bankListMock);

		creditCardTypeListMock = new ArrayList<>();
		creditCardTypeListMock.add(creditCardTypeMock);

	}

	@Test
	void findAllBankListByActiveTrue_Success() {
		Mockito.when(bankListRepository.findByActiveTrue()).thenReturn(bankListListMock);

		List<BankList> result = dataService.findAllBankListByActiveTrue(null);

		assertThat(result.size()).isEqualTo(1);
	}

	@Test
	void findAllCreditCardType_Success() {
		Mockito.when(cardTypeRepository.findAll()).thenReturn(creditCardTypeListMock);

		List<CreditCardType> result = cardTypeRepository.findAll();
		assertThat(result.size()).isEqualTo(1);
	}

}
