package my.com.mandrill;

import my.com.mandrill.utilities.general.constant.TimeConstant;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.util.TimeZone;

@SpringBootApplication
@EnableJpaRepositories(basePackages = { "my.com.mandrill.component.repository.jpa" })
public class UtilityComponentApplication {

	public static void main(String[] args) {
		TimeZone.setDefault(TimeZone.getTimeZone(TimeConstant.APPLICATION_TIMEZONE));
		SpringApplication.run(UtilityComponentApplication.class, args);
	}

}
