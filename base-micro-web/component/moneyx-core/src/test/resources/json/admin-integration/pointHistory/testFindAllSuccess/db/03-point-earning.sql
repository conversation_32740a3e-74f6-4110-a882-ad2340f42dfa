INSERT INTO moneyx_core.point_earning (id, created_date, created_by, last_modified_date, last_modified_by, user_id, user_ref_no, full_name, rsm_header_id, rsm_detail_id, point_amount, point_percentage, rsm_detail_focal_type, transaction_id, application_id, status, source, application_type, point_name, application_date, referral_code, rsm_scenario, point_disbursement_date, company_name, company_id, ref_no, application_ref_no) VALUES ('01JPP6PEVWF4YA1QWE8E6P46B5', '2025-03-19 03:21:03.612544', '0000000', '2025-03-19 03:21:16.838775', '0000000', null, null, null, '01JKAN5W93EH8VQ8J62N96P32A', '01JMEGDFQZKX215XE9AQ7RK61C', 50000.00, 50.00, 'MONEYX', null, '72f9a6b9-a047-44cf-b246-81dba589960d', 'AWARDED', 'MXAPP', 'LEAD_GEN', 'Referral Earnings', '2025-03-19 03:20:30.318462', null, 'C2C', '2025-03-19 03:21:16.833233', null, null, 'REMX2025030000001369', '****************');
INSERT INTO moneyx_core.point_earning (id, created_date, created_by, last_modified_date, last_modified_by, user_id, user_ref_no, full_name, rsm_header_id, rsm_detail_id, point_amount, point_percentage, rsm_detail_focal_type, transaction_id, application_id, status, source, application_type, point_name, application_date, referral_code, rsm_scenario, point_disbursement_date, company_name, company_id, ref_no, application_ref_no) VALUES ('01JPP6PEVWM32PC7PHCP1YTQGT', '2025-03-19 03:21:03.612285', '0000000', '2025-03-19 03:21:16.858478', '0000000', 'c0940afc-6bf2-46ad-bcff-c87e7a8d1f54', '**************', 'Anne Testttt', '01JKAN5W93EH8VQ8J62N96P32A', '01JMEGDC6ZTRM7HVK4ZYQY64T0', 15000.00, 15.00, 'REFEREE', null, '72f9a6b9-a047-44cf-b246-81dba589960d', 'AWARDED', 'MXAPP', 'LEAD_GEN', 'Alliance Bank Malaysia Berhad: Alliance Bank Visa Infinite Application Earnings - header2 2221', '2025-03-19 03:20:30.318462', 'TpPaO', 'C2C', '2025-03-19 03:21:16.850886', null, null, 'REMX2025030000001368', '****************');
INSERT INTO moneyx_core.point_earning (id, created_date, created_by, last_modified_date, last_modified_by, user_id, user_ref_no, full_name, rsm_header_id, rsm_detail_id, point_amount, point_percentage, rsm_detail_focal_type, transaction_id, application_id, status, source, application_type, point_name, application_date, referral_code, rsm_scenario, point_disbursement_date, company_name, company_id, ref_no, application_ref_no) VALUES ('01JPP6PEVWN9M4VCBKAER3QDFV', '2025-03-19 03:21:03.612635', '0000000', '2025-03-19 03:21:16.858636', '0000000', '19c52f11-1509-43f3-bd08-927728e8f92d', '**************', 'fahmi ', '01JKAN5W93EH8VQ8J62N96P32A', '01JMEGD19D8PJ5CQBE7DQF5SSH', 35000.00, 35.00, 'REFERRER_LEVEL_1', null, '72f9a6b9-a047-44cf-b246-81dba589960d', 'AWARDED', 'MXAPP', 'LEAD_GEN', 'Referral Earnings', '2025-03-19 03:20:30.318462', null, 'C2C', '2025-03-19 03:21:16.850917', null, null, 'REMX2025030000001370', '****************');
