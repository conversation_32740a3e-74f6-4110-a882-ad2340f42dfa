INSERT INTO moneyx_core.point_withdrawal
(id, created_date, created_by, last_modified_date, last_modified_by, application_id, user_id, user_ref_no, full_name, email, bank_name, account_holder_name, bank_account_no, payment_info_status, requested_amount, admin_fee, receivable_amount, status, failure_reason, cash_disbursement_date, "source", transaction_id)
VALUES('01JJ9XMTDEHTM1MD5Q71QTTYHH', '2025-01-23 22:55:08.141', 'sys-admin', '2025-01-23 22:55:08.141', NULL, '1', '6cdf1152-0499-4d5a-bc81-94bbd72e546e', '8888888', 'testingwd@localhost', 'testing', 'CIMB', 'WDTest', '1233333', 'APPROVED', 5000, 500, 4500, 'DISBURSED', NULL, NULL, 'MXAPP', '01JJ9XZHFHEAYVPG7E7HFBS9YW');

INSERT INTO moneyx_core.point_withdrawal
(id, created_date, created_by, last_modified_date, last_modified_by, application_id, user_id, user_ref_no, full_name, email, bank_name, account_holder_name, bank_account_no, payment_info_status, requested_amount, admin_fee, receivable_amount, status, failure_reason, cash_disbursement_date, "source", transaction_id)
VALUES('01JJ9Y0HQBFG89P9KMZPHG39WY', '2025-01-23 22:55:08.141', 'sys-admin', '2025-01-23 22:55:08.141', NULL, '1', '6cdf1152-0499-4d5a-bc81-94bbd72e546e', '8888888', 'testingwd@localhost', 'testing', 'CIMB', 'WDTest', '1233333', 'APPROVED', 5000, 500, 4500, 'PENDING', NULL, NULL, 'MXAPP', '01JJ9XZHFHEAYVPG7E7HFBS9YW');