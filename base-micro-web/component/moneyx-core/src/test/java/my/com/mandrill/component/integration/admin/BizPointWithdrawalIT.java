package my.com.mandrill.component.integration.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import my.com.mandrill.component.constant.PointWithdrawalStatus;
import my.com.mandrill.component.domain.PointWithdrawal;
import my.com.mandrill.component.dto.request.BulkUpdateWithdrawalStatusRequest;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.PointWithdrawalRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

class BizPointWithdrawalIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private PointWithdrawalRepository pointWithdrawalRepository;

	@Autowired
	private ObjectMapper objectMapper;

	@AfterEach
	public void tearDown() {
		pointWithdrawalRepository.deleteAll();
	}

	@Test
	void getAllWithdrawalManagementsSuccess() throws Exception {
		mockMvc.perform(get("/moneyxbiz-integration/admin/point-withdrawal/pagination/all")
				.header("hash", bizHashKeyGenerator.getHash()).header("identifier", bizHashKeyGenerator.getIdentifier())
				.header("timestamp", bizHashKeyGenerator.getInstant().toString()).param("search", "")
				.param("status", "PENDING").param("dateType", "CREATED_DATE"))
				.andExpect(MockMvcResultMatchers.status().isOk());
	}

	@Test
	void getWithdrawalByApplicationIdSuccess() throws Exception {
		mockMvc.perform(get("/moneyxbiz-integration/admin/point-withdrawal/PDAPP0000000043")
				.header("hash", bizHashKeyGenerator.getHash()).header("identifier", bizHashKeyGenerator.getIdentifier())
				.header("timestamp", bizHashKeyGenerator.getInstant().toString()))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.jsonPath("$.refNo").value("PDAPP0000000043"));
	}

	@Test
	void updateWithdrawalStatusByApplicationIdSuccess() throws Exception {
		String requestJson = getRequest();
		mockMvc.perform(put("/moneyxbiz-integration/admin/point-withdrawal/withdrawal-management/PDAPP0000000043")
				.header("hash", bizHashKeyGenerator.getHash()).header("identifier", bizHashKeyGenerator.getIdentifier())
				.header("timestamp", bizHashKeyGenerator.getInstant().toString())
				.contentType(MediaType.APPLICATION_JSON).content(requestJson))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		Optional<PointWithdrawal> updated = pointWithdrawalRepository.findByRefNo("PDAPP0000000043");
		Assertions.assertTrue(updated.isPresent());
		Assertions.assertEquals(PointWithdrawalStatus.DISBURSED, updated.get().getStatus());
	}

	@Test
	void bulkUpdateWithdrawalStatusByApplicationIdSuccess() throws Exception {
		BulkUpdateWithdrawalStatusRequest request = new BulkUpdateWithdrawalStatusRequest();
		request.setApplicationIds(Arrays.asList("PDAPP0000000043", "PDAPP0000000042"));
		request.setStatus(PointWithdrawalStatus.DISBURSED);

		mockMvc.perform(put("/moneyxbiz-integration/admin/point-withdrawal/withdrawal-management/bulk")
				.header("hash", bizHashKeyGenerator.getHash()).header("identifier", bizHashKeyGenerator.getIdentifier())
				.header("timestamp", bizHashKeyGenerator.getInstant().toString())
				.contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		List<PointWithdrawal> updated = pointWithdrawalRepository
				.findByRefNoIn(Arrays.asList("PDAPP0000000043", "PDAPP0000000042"));
		Assertions.assertEquals(2, updated.size());
		Assertions.assertEquals(PointWithdrawalStatus.DISBURSED, updated.get(0).getStatus());
		Assertions.assertEquals(PointWithdrawalStatus.DISBURSED, updated.get(1).getStatus());
	}

	@Test
	void getAllWithdrawalManagementForExportSuccess() throws Exception {
		mockMvc.perform(get("/moneyxbiz-integration/admin/point-withdrawal/withdrawal-management/export")
				.header("hash", bizHashKeyGenerator.getHash()).header("identifier", bizHashKeyGenerator.getIdentifier())
				.header("timestamp", bizHashKeyGenerator.getInstant().toString()).param("search", ""))
				.andExpect(MockMvcResultMatchers.status().isOk());
	}

	@Test
	void getAllWithdrawalManagementForExportInRefNoSuccess() throws Exception {
		mockMvc.perform(post("/moneyxbiz-integration/admin/point-withdrawal/withdrawal-management/export")
				.header("hash", bizHashKeyGenerator.getHash()).header("identifier", bizHashKeyGenerator.getIdentifier())
				.header("timestamp", bizHashKeyGenerator.getInstant().toString())
				.contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(List.of("PDAPP0000000043"))))
				.andExpect(MockMvcResultMatchers.status().isOk());
	}

}
