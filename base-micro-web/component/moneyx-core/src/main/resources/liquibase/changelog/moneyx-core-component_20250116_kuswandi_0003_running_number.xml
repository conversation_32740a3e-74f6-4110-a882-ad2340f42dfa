<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="moneyx-core-component_20250116_kuswandi_0003" author="kuswandi">
        <addColumn tableName="rsm_header">
            <column name="commission_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <createTable tableName="running_number">
            <column name="module_name" type="VARCHAR(100)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_running_number"/>
            </column>
            <column name="running_number" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="prefix" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="%d" name="number_format" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="include_day" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="institution_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="UTC" name="time_zone" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <loadUpdateData encoding="UTF-8" primaryKey="module_name"
                        file="liquibase/dataset/running-number/running-number-20250116-001.csv"
                        separator=";"
                        tableName="running_number"/>

        <sqlFile dbms="postgresql" path="liquibase/sql/running_number_sequence.sql" splitStatements="false"/>
    </changeSet>
</databaseChangeLog>