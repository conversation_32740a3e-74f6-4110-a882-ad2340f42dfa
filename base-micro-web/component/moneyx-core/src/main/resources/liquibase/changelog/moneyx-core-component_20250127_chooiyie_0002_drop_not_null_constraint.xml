<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="moneyx-core-component_20250127_chooiyie_0002" author="chooiyie">
        <dropNotNullConstraint tableName="rsm_header" columnName="product_category_id"/>
        <dropNotNullConstraint tableName="rsm_header" columnName="product_category_name"/>
        <dropNotNullConstraint tableName="rsm_header" columnName="product_type_id"/>
        <dropNotNullConstraint tableName="rsm_header" columnName="product_type_name"/>
        <dropNotNullConstraint tableName="rsm_header" columnName="product_provider_id"/>
        <dropNotNullConstraint tableName="rsm_header" columnName="product_provider_name"/>
        <dropNotNullConstraint tableName="rsm_header" columnName="product_id"/>
        <dropNotNullConstraint tableName="rsm_header" columnName="product_name"/>
    </changeSet>
</databaseChangeLog>