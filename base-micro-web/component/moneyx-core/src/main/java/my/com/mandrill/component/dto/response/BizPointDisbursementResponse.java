package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Data
public class BizPointDisbursementResponse extends PointDisbursementResponse implements Serializable {

	private String companyName;

	private String companyId;

}
