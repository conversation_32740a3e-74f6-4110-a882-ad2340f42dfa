package my.com.mandrill.edgeprop.controller.admin;

import my.com.mandrill.edgeprop.domain.EdgePropTransaction;
import my.com.mandrill.edgeprop.dto.request.EdgePropTransactionQuery;
import my.com.mandrill.edgeprop.service.EdgePropTransactionService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

@WebMvcTest(value = AdminEdgePropTransactionController.class,
		excludeAutoConfiguration = SecurityAutoConfiguration.class)
@AutoConfigureMockMvc
@ContextConfiguration(classes = AdminEdgePropTransactionController.class)
public class AdminEdgePropTransactionControllerTest {

	@Autowired
	private MockMvc mockMvc;

	@MockBean
	private EdgePropTransactionService edgePropTransactionService;

	@Test
	public void testFindAllSuccess() throws Exception {
		EdgePropTransaction edgePropTransaction = new EdgePropTransaction();
		edgePropTransaction.setId("0143c430-4232-4b54-bdec-fd792c93a926");
		Mockito.when(edgePropTransactionService.findAll(Mockito.any(Pageable.class),
				Mockito.any(EdgePropTransactionQuery.class))).thenReturn(new PageImpl<>(List.of(edgePropTransaction)));

		mockMvc.perform(
				MockMvcRequestBuilders.get("/admin/edge-prop-transactions").contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.jsonPath("$.content.length()").value(1))
				.andExpect(MockMvcResultMatchers.jsonPath("$.content[0].id")
						.value("0143c430-4232-4b54-bdec-fd792c93a926"));

		Mockito.verify(edgePropTransactionService, Mockito.times(1)).findAll(Mockito.any(Pageable.class),
				Mockito.any(EdgePropTransactionQuery.class));
	}

}
