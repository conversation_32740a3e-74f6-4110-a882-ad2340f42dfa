package my.com.mandrill.edgeprop.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.edgeprop.config.EdgePropMapper;
import my.com.mandrill.edgeprop.domain.EdgePropAsset;
import my.com.mandrill.edgeprop.dto.response.AssetResponse;
import my.com.mandrill.edgeprop.repository.jpa.EdgePropAssetRepository;
import my.com.mandrill.edgeprop.service.EdgePropAssetService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class EdgePropAssetServiceImpl implements EdgePropAssetService {

	private final EdgePropAssetRepository edgePropAssetRepository;

	@Override
	@Transactional
	public List<EdgePropAsset> save(List<AssetResponse> responses) {
		return responses.stream().map(assetResponse -> {
			EdgePropAsset edgePropAsset = EdgePropMapper.MAPPER.toEdgePropAsset(assetResponse);

			Optional<EdgePropAsset> existingEdgePropAsset = edgePropAssetRepository
					.findByAssetId(edgePropAsset.getAssetId());

			if (existingEdgePropAsset.isPresent()) {
				edgePropAsset.setId(existingEdgePropAsset.get().getId());
			}
			edgePropAsset = edgePropAssetRepository.save(edgePropAsset);
			return edgePropAsset;
		}).toList();
	}

	@Override
	public EdgePropAsset findById(String id) {
		return edgePropAssetRepository.findById(id).orElseThrow(ExceptionPredicate.edgePropAssetNotFound(id));
	}

}
