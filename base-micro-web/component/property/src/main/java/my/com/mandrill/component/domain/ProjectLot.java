package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;
import my.com.mandrill.utilities.core.audit.AuditSection;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "project_lot")
public class ProjectLot extends AuditSection {

	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JsonBackReference
	@JoinColumn(name = "project_id")
	private Project project;

	@Column(name = "no_of_lot")
	private Integer noOfLot;

	@Column(name = "house_per_lot")
	private Integer housePerLot;

	@Column(name = "number_of_floor")
	private Integer numberOfFloor;

}
