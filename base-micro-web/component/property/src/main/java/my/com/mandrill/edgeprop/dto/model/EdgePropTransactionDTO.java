package my.com.mandrill.edgeprop.dto.model;

import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;
import my.com.mandrill.edgeprop.constant.EdgePropAPI;

import java.io.Serializable;
import java.time.Instant;

@Data
public class EdgePropTransactionDTO implements Serializable {

	private String id;

	private Instant createdDate;

	private Instant lastModifiedDate;

	private String userId;

	private EdgePropAPI apiType;

	@JsonRawValue
	private String request;

	@JsonRawValue
	private String response;

	private String code;

	private String message;

	private String errors;

	private Long responseTimeInMs;

}