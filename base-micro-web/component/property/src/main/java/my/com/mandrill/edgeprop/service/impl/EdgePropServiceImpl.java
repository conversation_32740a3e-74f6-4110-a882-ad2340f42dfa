package my.com.mandrill.edgeprop.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.edgeprop.client.EdgePropClient;
import my.com.mandrill.edgeprop.constant.EdgePropAPI;
import my.com.mandrill.edgeprop.constant.EdgePropPropertyCategory;
import my.com.mandrill.edgeprop.context.EdgePropContext;
import my.com.mandrill.edgeprop.domain.EdgePropTransaction;
import my.com.mandrill.edgeprop.dto.request.EdgePropERPRequest;
import my.com.mandrill.edgeprop.dto.response.AssetResponse;
import my.com.mandrill.edgeprop.dto.response.ERPResponse;
import my.com.mandrill.edgeprop.exception.EdgePropException;
import my.com.mandrill.edgeprop.exception.ErrorCodeEnum;
import my.com.mandrill.edgeprop.service.EdgePropService;
import my.com.mandrill.edgeprop.service.EdgePropTransactionService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(value = "edge-prop.enabled", havingValue = "true")
public class EdgePropServiceImpl implements EdgePropService {

	private final EdgePropClient edgePropClient;

	private final EdgePropContext edgePropContext;

	private final ObjectMapper objectMapper;

	private final EdgePropTransactionService edgePropTransactionService;

	@Override
	public ERPResponse erpGetData(EdgePropERPRequest edgePropErpRequest, String userId) throws JsonProcessingException {
		ERPResponse response = null;
		long startTime = System.currentTimeMillis();
		EdgePropTransaction edgePropTransaction = new EdgePropTransaction();
		edgePropTransaction.setUserId(userId);
		edgePropTransaction.setApiType(EdgePropAPI.ERP);

		try {
			Map<String, String> request = objectMapper.convertValue(edgePropErpRequest, new TypeReference<>() {
			});

			edgePropContext.setAuthentication(request);
			request.put("listtype", "sale");
			request.put("source", "epiq");

			edgePropTransaction.setRequest(objectMapper.writeValueAsString(request));

			response = erpGetDataWrapper(request);
			edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
			edgePropTransaction.setResponse(objectMapper.writeValueAsString(response));

			if (edgePropContext.isSessionInvalidated(response.getSaleRtnCode())) {
				log.warn("Attempting to retry due to session invalidated");
				try {
					edgePropContext.createSession();
					edgePropContext.setAuthentication(request);
					response = erpGetDataWrapper(request);

					edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
					edgePropTransaction.setResponse(objectMapper.writeValueAsString(response));
				}
				catch (WebClientResponseException e) {
					edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
					edgePropTransaction.setResponse(e.getResponseBodyAsString());

					throw new EdgePropException(ErrorCodeEnum.UNKNOWN_ERROR_HAPPEN);
				}
				catch (Exception e) {
					log.error("Failed to retry. {}", e.getMessage());
					edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
					edgePropTransaction.setErrors(e.getMessage());

					throw new EdgePropException(ErrorCodeEnum.UNKNOWN_ERROR_HAPPEN);
				}
			}
		}
		catch (WebClientResponseException e) {
			edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
			edgePropTransaction.setResponse(e.getResponseBodyAsString());

			throw new EdgePropException(ErrorCodeEnum.UNKNOWN_ERROR_HAPPEN);
		}
		catch (Exception e) {
			edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
			edgePropTransaction.setErrors(e.getMessage());

			throw new EdgePropException(ErrorCodeEnum.UNKNOWN_ERROR_HAPPEN);
		}
		finally {
			if (Objects.nonNull(response)) {
				edgePropTransaction.setCode(response.getSaleRtnCode());
				edgePropTransaction.setMessage(response.getSaleRtnDesc());

				edgePropTransaction = edgePropTransactionService.save(edgePropTransaction);
				response.setTransactionId(edgePropTransaction.getId());
			}
		}
		return response;
	}

	@Override
	public List<AssetResponse> getAssets(EdgePropPropertyCategory edgePropPropertyCategory, String query, String state,
			String userId) {
		long startTime = System.currentTimeMillis();

		EdgePropTransaction edgePropTransaction = new EdgePropTransaction();
		edgePropTransaction.setUserId(userId);
		edgePropTransaction.setApiType(EdgePropAPI.ASSET);

		try {
			edgePropTransaction.setRequest(objectMapper.writeValueAsString(
					Map.of("propertyCategory", edgePropPropertyCategory.getCode(), "query", query, "state", state)));
			List<AssetResponse> responses = edgePropClient.getAssets(edgePropPropertyCategory.getCode(), null, query,
					state);

			log.info("EdgeProp Asset API took {} ms to response", System.currentTimeMillis() - startTime);

			edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
			edgePropTransaction.setResponse(objectMapper.writeValueAsString(responses));

			return responses;
		}
		catch (WebClientResponseException e) {
			edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
			edgePropTransaction.setResponse(e.getResponseBodyAsString());

			throw new EdgePropException(ErrorCodeEnum.UNKNOWN_ERROR_HAPPEN);
		}
		catch (Exception e) {
			edgePropTransaction.setResponseTimeInMs(System.currentTimeMillis() - startTime);
			edgePropTransaction.setErrors(e.getMessage());

			throw new EdgePropException(ErrorCodeEnum.UNKNOWN_ERROR_HAPPEN);
		}
		finally {
			edgePropTransactionService.asyncSave(edgePropTransaction);
		}
	}

	private ERPResponse erpGetDataWrapper(Map<String, String> request) {
		long startTime = System.currentTimeMillis();
		ERPResponse response = edgePropClient.erpGetData(request);
		log.info("EdgeProp ERP API took {} ms to response", System.currentTimeMillis() - startTime);
		return response;
	}

}
