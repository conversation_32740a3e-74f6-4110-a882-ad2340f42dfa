package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.UserSkipAssetResponse;
import my.com.mandrill.utilities.general.constant.EntityName;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface UserSkipAssetResponseRepository extends JpaRepository<UserSkipAssetResponse, String> {

	Optional<UserSkipAssetResponse> findByUserIdAndEntityName(String userId, EntityName entityName);

	List<UserSkipAssetResponse> findByUserId(String userId);

}
