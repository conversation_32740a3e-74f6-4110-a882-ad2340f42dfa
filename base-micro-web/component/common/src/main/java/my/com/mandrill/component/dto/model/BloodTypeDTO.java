package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BloodTypeDTO implements Serializable {

	private String id;

	@NotBlank
	@Size(max = 10)
	private String code;

	@NotBlank
	@Size(max = 20)
	private String name;

	@Size(max = 255)
	private String description;

	private Boolean active;

	private Integer sequence;

}
