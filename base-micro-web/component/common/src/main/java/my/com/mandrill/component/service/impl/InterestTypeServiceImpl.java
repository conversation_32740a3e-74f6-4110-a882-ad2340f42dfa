package my.com.mandrill.component.service.impl;

import my.com.mandrill.component.domain.InterestType;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.InterestTypeRepository;
import my.com.mandrill.component.service.InterestTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class InterestTypeServiceImpl implements InterestTypeService {

	private final InterestTypeRepository interestTypeRepository;

	public InterestTypeServiceImpl(InterestTypeRepository interestTypeRepository) {
		this.interestTypeRepository = interestTypeRepository;
	}

	@Override
	public List<InterestType> findAll() {
		return interestTypeRepository.findAll();
	}

	@Override
	public InterestType findOne(String id) {
		return interestTypeRepository.findById(id).orElseThrow(ExceptionPredicate.interestTypeNotFoundById(id));
	}

	@Transactional
	@Override
	public InterestType save(InterestType interestType) {
		return interestTypeRepository.save(interestType);
	}

}
