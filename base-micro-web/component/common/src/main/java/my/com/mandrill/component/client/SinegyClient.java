package my.com.mandrill.component.client;

import my.com.mandrill.component.dto.response.SinegyDaxMarketDataResponse;
import my.com.mandrill.component.dto.response.SinegyDaxResponse;
import org.springframework.http.MediaType;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

@HttpExchange(contentType = MediaType.APPLICATION_JSON_VALUE, accept = MediaType.APPLICATION_JSON_VALUE)
public interface SinegyClient {

	@GetExchange("market/get-market-summary")
	SinegyDaxResponse<SinegyDaxMarketDataResponse> getMarketSummary();

}
