package my.com.mandrill.component.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.domain.ProductSuggestion;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductSuggestionCategoryResponse {

	private String id;

	private String name;

	private String description;

	@JsonIgnoreProperties(value = { "productSuggestionCategory" })
	private List<ProductSuggestion> productSuggestions = new ArrayList<>();

}
