package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.BloodType;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BloodTypeRepository extends JpaRepository<BloodType, String> {

	Optional<BloodType> findByIdAndActiveTrue(String id);

	List<BloodType> findByActiveTrue(Sort sort);

	Optional<BloodType> findByCodeAndActiveTrue(String code);

}
