package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.SystemConfiguration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SystemConfigurationRepository extends JpaRepository<SystemConfiguration, String> {

	@Query("SELECT sc FROM SystemConfiguration sc WHERE LOWER(sc.code) LIKE %:code% AND (:institutionId IS NULL OR sc.institutionId = :institutionId)")
	Page<SystemConfiguration> findAllByCodeAndInstitutionId(Pageable page, String code, String institutionId);

	List<SystemConfiguration> findAllByInstitutionId(String institutionId);

	Optional<SystemConfiguration> findByCodeAndInstitutionIdAndActiveTrue(String code, String institutionId);

	Optional<SystemConfiguration> findByIdAndInstitutionId(String systemConfigurationId, String institutionId);

	Optional<SystemConfiguration> findByCodeAndActiveTrue(String code);

	boolean existsByCodeAllIgnoreCaseAndInstitutionId(String code, String institutionId);

}
