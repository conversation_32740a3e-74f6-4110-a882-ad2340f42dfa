<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.19.xsd">
    <changeSet id="common-component_20230619_wuikeat_0001" author="wuikeat">

        <createTable tableName="finance_type">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_finance_type"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="code" constraintName="uc_4fb98499a65573e7af2dfaef9" tableName="finance_type"/>

        <createTable tableName="property_document_type">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_property_document_type"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="code" constraintName="uc_bce09bab1fe57b77f0f218cca" tableName="property_document_type"/>

        <createTable tableName="vehicle_document_type">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_vehicle_document_type"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="code" constraintName="uc_58e36fa25f762b97f7f8e3dac" tableName="vehicle_document_type"/>

        <createTable tableName="legal_document_type">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_legal_document_type"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="code" constraintName="uc_1dc9654d3a78cb394201df6f9" tableName="legal_document_type"/>

        <loadData encoding="UTF-8"
                  file="liquibase/dataset/finance_type.csv"
                  separator=";"
                  tableName="finance_type"/>
        <loadData encoding="UTF-8"
                  file="liquibase/dataset/property_type.csv"
                  separator=";"
                  tableName="property_document_type"/>
        <loadData encoding="UTF-8"
                  file="liquibase/dataset/vehicle_type.csv"
                  separator=";"
                  tableName="vehicle_document_type"/>
        <loadData encoding="UTF-8"
                  file="liquibase/dataset/legal_type.csv"
                  separator=";"
                  tableName="legal_document_type"/>

    </changeSet>
</databaseChangeLog>