<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.19.xsd">

    <changeSet id="common-component_20230425_jose_0001" author="<PERSON>">
        <delete tableName="journey_configuration_interest_type" schemaName=""/>
        <sql>
            DELETE FROM JOURNEY_CONFIGURATION WHERE NAME = 'PROPERTY'
        </sql>
        <sql>
            DELETE FROM JOURNEY_CONFIGURATION WHERE NAME = 'MY_BANK'
        </sql>
    </changeSet>
</databaseChangeLog>