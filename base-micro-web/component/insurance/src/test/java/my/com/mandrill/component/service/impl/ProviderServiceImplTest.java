package my.com.mandrill.component.service.impl;

import my.com.mandrill.component.domain.Provider;
import my.com.mandrill.component.repository.jpa.ProviderRepository;
import my.com.mandrill.utilities.general.constant.InsuranceCategoryEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

public class ProviderServiceImplTest {

	@Mock
	private ProviderRepository providerRepository;

	@InjectMocks
	private ProviderServiceImpl providerService;

	@BeforeEach
	void setUp() {
		MockitoAnnotations.openMocks(this);
	}

	@Test
	void testFindByCategoryAndActiveTrue() {
		InsuranceCategoryEnum category = InsuranceCategoryEnum.LIFE;
		Sort sort = Sort.by("name");
		Provider provider = new Provider();
		provider.setCategory(category);
		provider.setActive(true);

		when(providerRepository.findByCategoryAndActiveTrue(category, sort)).thenReturn(List.of(provider));

		List<Provider> result = providerService.findByCategoryAndActiveTrue(category, sort);

		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals(category, result.get(0).getCategory());
	}

	@Test
	void testFindById_ExistingId() {
		String id = "providerId";
		Provider provider = new Provider();
		provider.setId(id);

		when(providerRepository.findById(id)).thenReturn(Optional.of(provider));

		Provider result = providerService.findById(id);

		assertNotNull(result);
		assertEquals(id, result.getId());
	}

	@Test
	void testCreate_ProviderExists() {
		Provider provider = new Provider();
		provider.setName("ProviderName");
		provider.setCategory(InsuranceCategoryEnum.LIFE);

		when(providerRepository.existsByNameIgnoreCaseAndCategory(provider.getName(), provider.getCategory()))
				.thenReturn(true);

		assertThrows(BusinessException.class, () -> providerService.create(provider));
	}

	@Test
	void testUpdate_SuccessfulUpdate() {
		Provider existingProvider = new Provider();
		existingProvider.setId("providerId");
		existingProvider.setName("OldName");
		existingProvider.setCategory(InsuranceCategoryEnum.LIFE);

		Provider newProvider = new Provider();
		newProvider.setId("providerId");
		newProvider.setName("NewName");
		newProvider.setCategory(InsuranceCategoryEnum.LIFE);

		when(providerRepository.findById(existingProvider.getId())).thenReturn(Optional.of(existingProvider));

		Provider result = providerService.update(newProvider);

		assertNotNull(result);
		assertEquals(newProvider.getName(), result.getName());
	}

	@Test
	void testDeactivateById() {
		String id = "providerId";
		Provider provider = new Provider();
		provider.setId(id);
		provider.setActive(true);

		when(providerRepository.findById(id)).thenReturn(Optional.of(provider));

		Provider result = providerService.deactivateById(id);

		assertNotNull(result);
		assertFalse(result.getActive());
	}

	@Test
	void testExistsByNameAndCategory_ProviderExists() {
		String name = "ExistingProvider";
		InsuranceCategoryEnum category = InsuranceCategoryEnum.GENERAL;

		when(providerRepository.existsByNameIgnoreCaseAndCategory(name, category)).thenReturn(true);

		assertThrows(BusinessException.class, () -> providerService.existsByNameAndCategory(name, category));
	}

	@Test
	void testFindAllIssuerCodeByIsPartnerTrue() {
		Provider provider1 = new Provider();
		provider1.setIssuerCode("Code1");
		provider1.setIsPartner(true);

		Provider provider2 = new Provider();
		provider2.setIssuerCode("Code2");
		provider2.setIsPartner(true);

		when(providerRepository.findAllByIsPartnerTrue()).thenReturn(List.of(provider1, provider2));

		String result = providerService.findAllIssuerCodeByIsPartnerTrue();

		assertNotNull(result);
		assertEquals("Code1,Code2", result);
	}

	@Test
	void testFindByIssuerCode_ExistingCode() {
		String issuerCode = "Code123";
		Provider provider = new Provider();
		provider.setIssuerCode(issuerCode);

		when(providerRepository.findFirstByIssuerCode(issuerCode)).thenReturn(Optional.of(provider));

		Provider result = providerService.findByIssuerCode(issuerCode);

		assertNotNull(result);
		assertEquals(issuerCode, result.getIssuerCode());
	}

}