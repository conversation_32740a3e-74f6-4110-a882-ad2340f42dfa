package my.com.mandrill.component.service.impl;

import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.domain.Email;
import my.com.mandrill.component.domain.EmailTemplate;
import my.com.mandrill.component.domain.Inbox;
import my.com.mandrill.component.dto.request.EmailContactUsRequest;
import my.com.mandrill.component.dto.request.EmailRequest;
import my.com.mandrill.component.service.EmailService;
import my.com.mandrill.component.service.EmailTemplateService;
import my.com.mandrill.component.service.InboxService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mail.javamail.JavaMailSender;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@SpringBootTest(classes = EmailIntegrationServiceImpl.class)
class EmailIntegrationServiceImplTest {

	@Autowired
	private EmailIntegrationServiceImpl emailIntegrationService;

	@MockBean
	private BaseProperties baseProperties;

	@MockBean
	private EmailTemplateService emailTemplateService;

	@MockBean
	private JavaMailSender javaMailSender;

	@MockBean
	private InboxService inboxService;

	@MockBean
	private EmailService emailService;

	@BeforeEach
	public void setUp() {
		var basePropertyMail = new BaseProperties.Mail();
		basePropertyMail.setEnabled(true);

		Mockito.when(baseProperties.getMail()).thenReturn(basePropertyMail);
		Mockito.when(baseProperties.getActiveProfile()).thenReturn("dev");
		Mockito.doNothing().when(javaMailSender).send(Mockito.any(MimeMessage.class));
	}

	@Test
	void testProcessEmailSuccess() {
		Mockito.when(baseProperties.getActiveProfile()).thenReturn("prod");

		Mockito.when(javaMailSender.createMimeMessage()).thenReturn(new MimeMessage((Session) null));

		EmailTemplate emailTemplate = new EmailTemplate();
		emailTemplate.setId("170ac68c-3879-4540-b6c0-adeae6db35a3");
		emailTemplate.setFrom("<EMAIL>");
		emailTemplate.setSubject("Please verify");
		emailTemplate.setTemplate("Your OTP: ");
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.of(emailTemplate));

		Assertions.assertDoesNotThrow(
				() -> emailIntegrationService.processEmail(EmailRequest.builder().saveToInbox(Boolean.TRUE)
						.templateName("OTP_VERIFICATION").userId("43318e98-6fa3-473b-8c79-2972b9ad0c17")
						.to(List.of("<EMAIL>")).cc(List.of("<EMAIL>")).build()));

		Mockito.verify(baseProperties, Mockito.times(1)).getMail();
		Mockito.verify(baseProperties, Mockito.times(1)).getActiveProfile();
		Mockito.verify(javaMailSender, Mockito.times(1)).createMimeMessage();
		Mockito.verify(emailTemplateService, Mockito.times(1)).findByActiveName(Mockito.anyString());
		Mockito.verify(inboxService, Mockito.times(1)).saveOrUpdate(Mockito.any(Inbox.class));
	}

	@Test
	void testProcessEmailToIsSetSuccess() {
		Mockito.when(baseProperties.getActiveProfile()).thenReturn("prod");

		Mockito.when(javaMailSender.createMimeMessage()).thenReturn(new MimeMessage((Session) null));

		EmailTemplate emailTemplate = new EmailTemplate();
		emailTemplate.setId("170ac68c-3879-4540-b6c0-adeae6db35a3");
		emailTemplate.setFrom("<EMAIL>");
		emailTemplate.setSubject("Please verify");
		emailTemplate.setTemplate("Your OTP: ");
		emailTemplate.setTo("<EMAIL>");
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.of(emailTemplate));

		Assertions.assertDoesNotThrow(
				() -> emailIntegrationService.processEmail(EmailRequest.builder().saveToInbox(Boolean.TRUE)
						.templateName("OTP_VERIFICATION").userId("43318e98-6fa3-473b-8c79-2972b9ad0c17")
						.to(List.of("<EMAIL>")).cc(List.of("<EMAIL>")).build()));

		Mockito.verify(baseProperties, Mockito.times(1)).getMail();
		Mockito.verify(baseProperties, Mockito.times(1)).getActiveProfile();
		Mockito.verify(javaMailSender, Mockito.times(1)).createMimeMessage();
		Mockito.verify(emailTemplateService, Mockito.times(1)).findByActiveName(Mockito.anyString());
		Mockito.verify(inboxService, Mockito.times(1)).saveOrUpdate(Mockito.any(Inbox.class));
	}

	@Test
	void testProcessEmailErrorWhenSaveResponse() {
		Mockito.when(baseProperties.getActiveProfile()).thenReturn("prod");

		Mockito.when(javaMailSender.createMimeMessage()).thenReturn(new MimeMessage((Session) null));

		Mockito.when(emailService.save(Mockito.any(Email.class)))
				.thenThrow(new RuntimeException("expected error when save"));

		EmailTemplate emailTemplate = new EmailTemplate();
		emailTemplate.setId("170ac68c-3879-4540-b6c0-adeae6db35a3");
		emailTemplate.setFrom("<EMAIL>");
		emailTemplate.setSubject("Please verify");
		emailTemplate.setTemplate("Your OTP: ");
		emailTemplate.setTo("<EMAIL>");
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.of(emailTemplate));

		EmailRequest emailRequest = EmailRequest.builder().saveToInbox(Boolean.TRUE).templateName("OTP_VERIFICATION")
				.userId("43318e98-6fa3-473b-8c79-2972b9ad0c17").to(List.of("<EMAIL>"))
				.cc(List.of("<EMAIL>")).build();
		Assertions.assertThrows(BusinessException.class, () -> emailIntegrationService.processEmail(emailRequest));

		Mockito.verify(baseProperties, Mockito.times(1)).getMail();
		Mockito.verify(baseProperties, Mockito.times(1)).getActiveProfile();
		Mockito.verify(javaMailSender, Mockito.times(1)).createMimeMessage();
		Mockito.verify(emailTemplateService, Mockito.times(1)).findByActiveName(Mockito.anyString());
	}

	@Test
	void testProcessEmailTemplateNotFound() {
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.empty());

		Assertions.assertDoesNotThrow(() -> {
			emailIntegrationService.processEmail(
					EmailRequest.builder().saveToInbox(Boolean.TRUE).templateName("OTP_VERIFICATION").build());
		});

		Mockito.verify(emailTemplateService, Mockito.times(1)).findByActiveName(Mockito.anyString());
	}

	@Test
	void testProcessEmailErrorWhenSend() {
		Mockito.when(baseProperties.getActiveProfile()).thenReturn("prod");

		Mockito.when(javaMailSender.createMimeMessage()).thenReturn(new MimeMessage((Session) null));

		Mockito.doThrow(new RuntimeException("expected error")).when(javaMailSender)
				.send(Mockito.any(MimeMessage.class));

		EmailTemplate emailTemplate = new EmailTemplate();
		emailTemplate.setId("170ac68c-3879-4540-b6c0-adeae6db35a3");
		emailTemplate.setFrom("<EMAIL>");
		emailTemplate.setSubject("Please verify");
		emailTemplate.setTemplate("Your OTP: ");
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.of(emailTemplate));

		Assertions.assertDoesNotThrow(
				() -> emailIntegrationService.processEmail(EmailRequest.builder().saveToInbox(Boolean.TRUE)
						.templateName("OTP_VERIFICATION").userId("43318e98-6fa3-473b-8c79-2972b9ad0c17")
						.to(List.of("<EMAIL>")).cc(List.of("<EMAIL>")).build()));

		Mockito.verify(baseProperties, Mockito.times(1)).getMail();
		Mockito.verify(baseProperties, Mockito.times(1)).getActiveProfile();
		Mockito.verify(javaMailSender, Mockito.times(1)).createMimeMessage();
		Mockito.verify(emailTemplateService, Mockito.times(1)).findByActiveName(Mockito.anyString());
		Mockito.verify(inboxService, Mockito.times(1)).saveOrUpdate(Mockito.any(Inbox.class));
	}

	@Test
	void testProcessContactUsEmailSuccess() {
		Mockito.when(baseProperties.getActiveProfile()).thenReturn("prod");

		Mockito.when(javaMailSender.createMimeMessage()).thenReturn(new MimeMessage((Session) null));

		Mockito.doThrow(new RuntimeException("expected error")).when(javaMailSender)
				.send(Mockito.any(MimeMessage.class));

		EmailTemplate emailTemplate = new EmailTemplate();
		emailTemplate.setId("170ac68c-3879-4540-b6c0-adeae6db35a3");
		emailTemplate.setFrom("<EMAIL>");
		emailTemplate.setSubject("Please verify");
		emailTemplate.setTemplate("Your OTP: !{otpCode}");
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.of(emailTemplate));

		Assertions.assertDoesNotThrow(() -> emailIntegrationService.processContactUsEmail(
				EmailContactUsRequest.builder().templateName("OTP_VERIFICATION").recipientEmail("<EMAIL>")
						.templateVariable(Map.of("otpCode", "11231")).timeTriggered(Instant.now()).build()));
	}

	@Test
	void testProcessContactUsEmailDataVariableNull() {
		Mockito.when(baseProperties.getActiveProfile()).thenReturn("prod");

		Mockito.when(javaMailSender.createMimeMessage()).thenReturn(new MimeMessage((Session) null));

		Mockito.doThrow(new RuntimeException("expected error")).when(javaMailSender)
				.send(Mockito.any(MimeMessage.class));

		EmailTemplate emailTemplate = new EmailTemplate();
		emailTemplate.setId("170ac68c-3879-4540-b6c0-adeae6db35a3");
		emailTemplate.setFrom("<EMAIL>");
		emailTemplate.setSubject("Please verify");
		emailTemplate.setTemplate("Your OTP: !{otpCode}");
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.of(emailTemplate));

		Assertions.assertDoesNotThrow(() -> emailIntegrationService
				.processContactUsEmail(EmailContactUsRequest.builder().templateName("OTP_VERIFICATION")
						.recipientEmail("<EMAIL>").templateVariable(null).timeTriggered(Instant.now()).build()));
	}

	@Test
	void testProcessContactUsEmailTemplateNotFound() {
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.empty());

		Assertions.assertDoesNotThrow(() -> emailIntegrationService.processContactUsEmail(
				EmailContactUsRequest.builder().templateName("OTP_VERIFICATION").recipientEmail("<EMAIL>")
						.templateVariable(Map.of()).timeTriggered(Instant.now()).build()));

		Mockito.verify(emailTemplateService, Mockito.times(1)).findByActiveName(Mockito.anyString());
	}

	@Test
	void testProcessContactUsEmailError() {
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString()))
				.thenThrow(new IllegalArgumentException("data not fund"));

		EmailContactUsRequest emailContactUsRequest = EmailContactUsRequest.builder().templateName("OTP_VERIFICATION")
				.recipientEmail("<EMAIL>").templateVariable(Map.of()).timeTriggered(Instant.now()).build();
		Assertions.assertThrows(BusinessException.class,
				() -> emailIntegrationService.processContactUsEmail(emailContactUsRequest));

		Mockito.verify(emailTemplateService, Mockito.times(1)).findByActiveName(Mockito.anyString());
	}

	@Test
	void testProcessContactUsEmailEnvNonProdSuccess() {
		Mockito.when(javaMailSender.createMimeMessage()).thenReturn(new MimeMessage((Session) null));

		Mockito.doThrow(new RuntimeException("expected error")).when(javaMailSender)
				.send(Mockito.any(MimeMessage.class));

		EmailTemplate emailTemplate = new EmailTemplate();
		emailTemplate.setId("170ac68c-3879-4540-b6c0-adeae6db35a3");
		emailTemplate.setFrom("<EMAIL>");
		emailTemplate.setSubject("Please verify");
		emailTemplate.setTemplate("Your OTP: !{otpCode}");
		emailTemplate.setTo("<EMAIL>");
		Mockito.when(emailTemplateService.findByActiveName(Mockito.anyString())).thenReturn(Optional.of(emailTemplate));

		Assertions.assertDoesNotThrow(() -> emailIntegrationService.processContactUsEmail(
				EmailContactUsRequest.builder().templateName("OTP_VERIFICATION").recipientEmail("<EMAIL>")
						.templateVariable(Map.of("otpCode", "11231")).timeTriggered(Instant.now()).build()));
	}

}
