package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Task;
import my.com.mandrill.utilities.general.constant.ActionType;
import my.com.mandrill.utilities.general.constant.TaskStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface TaskRepository extends JpaRepository<Task, String> {

	long countByUserId(@NonNull String userId);

	long countByUserIdAndStatusNot(@NonNull String userId, @NonNull TaskStatus status);

	long countByUserIdAndStatus(@NonNull String userId, @NonNull TaskStatus status);

	@Query("""
			select (count(t) > 0) from Task t
			where t.reminderId = :reminderId
			and cast(t.taskDate as date) = cast(:taskDate as date)
			""")
	boolean existsByReminderIdAndTaskDate(@NonNull String reminderId, @NonNull Instant taskDate);

	long countByUserIdAndReminderIdAndStatusInAndTaskDateBetween(@NonNull String userId, @NonNull String reminderId,
			@NonNull Collection<TaskStatus> statuses, @NonNull Instant taskDateStart, @NonNull Instant taskDateEnd);

	long countByUserIdAndReminderIdAndStatusInAndTaskDateGreaterThan(@NonNull String userId, @NonNull String reminderId,
			@NonNull Collection<TaskStatus> statuses, @NonNull Instant taskDate);

	Optional<Task> findFirstByReminderIdOrderByTaskDateDesc(@NonNull String reminderId);

	Optional<Task> findByIdAndReminderIdAndUserId(String id, String reminderId, String userId);

	long deleteByReminderIdAndStatus(@NonNull String reminderId, @NonNull TaskStatus status);

	List<Task> findByTaskDateLessThanEqualAndStatusInOrderByUserIdAscTaskDateAsc(@NonNull Instant taskDate,
			@NonNull Collection<TaskStatus> statuses);

	List<Task> findByUserIdAndReminderIdNotNullAndTaskDateBetweenOrderByTaskDateAsc(@NonNull String userId,
			@NonNull Instant taskDateStart, @NonNull Instant taskDateEnd);

	Page<Task> findByUserIdAndReminderIdNotNullOrderByTaskDateAsc(@NonNull Pageable pageable, @NonNull String userId);

	List<Task> findByStatusAndReminderIdAndUserId(@NonNull TaskStatus status, @NonNull String reminderId,
			@NonNull String userId);

	@Modifying
	@Query("UPDATE Task t SET t.status = :status WHERE t.entityId = :entityId AND t.actionType = :actionType")
	int updateStatusByEntityIdAndActionType(TaskStatus status, String entityId, ActionType actionType);

	@Modifying
	@Query("UPDATE Task t SET t.status = my.com.mandrill.utilities.general.constant.TaskStatus.COMPLETED "
			+ "WHERE t.autoCompleteDate < :date "
			+ "AND t.status = my.com.mandrill.utilities.general.constant.TaskStatus.PENDING")
	int updateCompletedTaskStatusByTaskDateLessThan(Instant date);

}