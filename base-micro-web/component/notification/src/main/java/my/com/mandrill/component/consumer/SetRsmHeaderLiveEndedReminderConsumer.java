package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.service.InboxService;
import my.com.mandrill.utilities.feign.dto.request.RsmHeaderLiveEndedRequest;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SetRsmHeaderLiveEndedReminderConsumer {

	private final JSONUtil jsonUtil;

	private final InboxService inboxService;

	@KafkaListener(topics = KafkaTopic.SET_RSM_HEADER_LIVE_ENDED_REMINDER_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.SET_RSM_HEADER_LIVE_ENDED_REMINDER_TOPIC)
	public void consume(String message) {
		try {
			log.info("start consume topic: {}, message: {}", KafkaTopic.SET_RSM_HEADER_LIVE_ENDED_REMINDER_TOPIC,
					message);
			RsmHeaderLiveEndedRequest rsmHeaderLiveEndedRequest = jsonUtil.convertValueFromJson(message,
					new TypeReference<>() {
					});
			inboxService.saveRsmLiveEndedReminder(rsmHeaderLiveEndedRequest);
		}
		catch (Exception e) {
			log.error("feature=send-rsm-header-live-ended-reminder, message {}, error:{}", message, e.getMessage(), e);
		}
	}

}
