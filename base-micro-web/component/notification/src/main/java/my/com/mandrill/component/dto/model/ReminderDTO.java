package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import my.com.mandrill.utilities.general.constant.ReminderFrequency;

import java.time.Instant;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReminderDTO extends AbstractReminderDTO {

	private Instant startDate;

	private Instant endDate;

	private Instant nextExecutionDate;

	private ReminderFrequency reminderFrequency;

	private Boolean isIntegration;

	private Boolean editable;

}
