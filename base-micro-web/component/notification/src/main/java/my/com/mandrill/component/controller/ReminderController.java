package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Reminder;
import my.com.mandrill.component.dto.model.ReminderDTO;
import my.com.mandrill.component.dto.request.CreateReminderRequest;
import my.com.mandrill.component.dto.request.GetRemindersParams;
import my.com.mandrill.component.dto.request.ReminderIntegrationRequest;
import my.com.mandrill.component.dto.request.UpdateReminderRequest;
import my.com.mandrill.component.dto.response.EntityWithoutReminderResponse;
import my.com.mandrill.component.service.PopulateService;
import my.com.mandrill.component.service.ReminderIntegrationService;
import my.com.mandrill.component.service.ReminderService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.ReminderCheckDTO;
import my.com.mandrill.utilities.feign.dto.UserDTO;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.ReminderType;
import my.com.mandrill.utilities.general.dto.ValidList;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequestMapping("reminders")
@RequiredArgsConstructor
public class ReminderController {

	private final AccountFeignClient accountFeignClient;

	private final ObjectMapper objectMapper;

	private final PopulateService populateService;

	private final ReminderService reminderService;

	private final ReminderIntegrationService reminderIntegrationService;

	private final ValidationService validationService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<ReminderDTO> create(@Valid @RequestBody CreateReminderRequest createReminderRequest) {
		Reminder reminder = MapStructConverter.MAPPER.toReminder(createReminderRequest);
		validationService.validateCreateReminder(reminder);

		UserDTO user = objectMapper.convertValue(accountFeignClient.getCurrentUserId(), UserDTO.class);

		Reminder result = reminderIntegrationService.create(reminder, user);

		populateService.populateAbstractReminder(result);
		reminderIntegrationService.sendDashboardActivity(result.getCreatedDate());
		return ResponseEntity.ok(objectMapper.convertValue(result, ReminderDTO.class));
	}

	@Hidden
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("integration")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void integration(@Valid @RequestBody ReminderIntegrationRequest reminderIntegrationRequest) {
		log.info("ReminderController: integration START");
		Reminder reminder = MapStructConverter.MAPPER.toReminder(reminderIntegrationRequest);
		validationService.validateCreateReminder(reminder);

		UserDTO user = objectMapper.convertValue(accountFeignClient.getCurrentUserId(), UserDTO.class);

		switch (reminderIntegrationRequest.getReminderType()) {
			case EXPENSE -> user.setExpenses(List.of(reminderIntegrationRequest.getExpense()));
			case INCOME -> user.setIncomes(List.of(reminderIntegrationRequest.getIncome()));
			case VEHICLE_INSTALMENT -> user.setVehicle(reminderIntegrationRequest.getVehicle());
			case INSURANCE_INSTALMENT -> user.setInsurance(reminderIntegrationRequest.getInsurance());
			case LOAN_INSTALMENT -> user.setLoan(reminderIntegrationRequest.getLoan());
			case E_ACCESS -> user.setAccess(reminderIntegrationRequest.getAccess());
			default -> log.info("Integrating without using UserDTO.");
		}
		reminder.setIsIntegration(true);
		Reminder result = reminderIntegrationService.create(reminder, user);
		reminderIntegrationService.sendDashboardActivity(result.getCreatedDate());
		log.info("ReminderController: integration STOP");
	}

	@Hidden
	@PutMapping("integration/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<ReminderDTO> updateIntegration(
			@Valid @RequestBody UpdateReminderRequest updateReminderRequest, @PathVariable String id) {
		log.info("ReminderController: integration update START");
		Reminder reminder = MapStructConverter.MAPPER.toReminder(updateReminderRequest);
		reminder.setId(id);

		UserDTO user = objectMapper.convertValue(accountFeignClient.getCurrentUserId(), UserDTO.class);

		validationService.validateUpdateReminderIntegration(reminder, user);
		Reminder result = reminderIntegrationService.update(reminder, user);

		populateService.populateAbstractReminder(result);
		log.info("ReminderController: integration update STOP");
		return ResponseEntity.ok(objectMapper.convertValue(result, ReminderDTO.class));
	}

	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<ReminderDTO> update(@Valid @RequestBody UpdateReminderRequest updateReminderRequest,
			@PathVariable String id) {
		Reminder reminder = MapStructConverter.MAPPER.toReminder(updateReminderRequest);
		reminder.setId(id);
		UserDTO user = objectMapper.convertValue(accountFeignClient.getCurrentUserId(), UserDTO.class);

		validationService.validateUpdateReminder(reminder, user);
		Reminder result = reminderIntegrationService.update(reminder, user);

		// backward compatible, FE will assume title as name
		result.setTitle(result.getName());
		populateService.populateAbstractReminder(result);
		return ResponseEntity.ok(objectMapper.convertValue(result, ReminderDTO.class));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<ReminderDTO>> findByParams(
			@SortDefault(sort = "reminderType", direction = Sort.Direction.ASC) Sort sort, GetRemindersParams params) {
		UserDTO user = objectMapper.convertValue(accountFeignClient.getCurrentUserId(), UserDTO.class);

		List<Reminder> reminders = reminderService.findByParams(sort, user, params);
		return ResponseEntity.ok(reminders.stream().map(reminder -> {
			populateService.populateAbstractReminder(reminder);
			ReminderDTO result = objectMapper.convertValue(reminder, ReminderDTO.class);

			// backward compatible, FE will assume title as name
			result.setTitle(reminder.getName());
			return result;
		}).toList());
	}

	@GetMapping("{reminderType}/{dataId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<ReminderDTO> findByReminderTypeAndDataId(@PathVariable ReminderType reminderType,
			@PathVariable String dataId,
			@RequestParam(required = false, defaultValue = "true") boolean populateEntity) {
		UserDTO user = objectMapper.convertValue(accountFeignClient.getCurrentUserId(), UserDTO.class);

		Reminder result = reminderService.findByReminderTypeAndDataId(reminderType, dataId, user.getId());

		if (populateEntity) {
			populateService.populateAbstractReminder(result);
		}
		ReminderDTO data = objectMapper.convertValue(result, ReminderDTO.class);

		// backward compatible, FE will assume title as name
		data.setTitle(result.getName());
		return ResponseEntity.ok(data);
	}

	@DeleteMapping("{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void delete(@PathVariable String id) {
		String userId = SecurityUtil.currentUserId();
		reminderService.delete(reminderService.findById(id, userId), userId);
	}

	@GetMapping("types")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<ReminderType.ReminderTypeDTO>> findAllReminderType(
			@RequestParam(required = false) String name) {
		List<ReminderType.ReminderTypeDTO> result = Stream.of(ReminderType.values())
				.filter(el -> !el.getCode().equals(ReminderType.PRE_PAID_TOP_UP.getCode())).map(ReminderType::getObject)
				.sorted(Comparator.comparing(ReminderType.ReminderTypeDTO::getName)).toList();

		if (StringUtils.isNotBlank(name)) {
			return ResponseEntity.ok(result.stream()
					.filter(reminderTypeDTO -> StringUtils.containsIgnoreCase(reminderTypeDTO.getName(), name))
					.toList());
		}

		return ResponseEntity.ok(result);
	}

	@GetMapping("no-reminder-entity/{reminderType}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<EntityWithoutReminderResponse> findAllEntityWithoutReminder(
			@PathVariable ReminderType reminderType, @RequestParam(required = false) String id) {
		EntityWithoutReminderResponse result = reminderIntegrationService.findAllEntityWithoutReminder(reminderType,
				id);
		return ResponseEntity.ok(result);
	}

	@DeleteMapping("{reminderType}/{dataId}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void deleteByReminderTypeAndDataId(@PathVariable ReminderType reminderType, @PathVariable String dataId) {
		String userId = SecurityUtil.currentUserId();

		try {
			reminderService.delete(reminderService.findByReminderTypeAndDataId(reminderType, dataId, userId), userId);
		}
		catch (EntityNotFoundException e) {
			log.debug(e.getMessage());
		}

	}

	@PostMapping("bulk")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public List<ReminderDTO> create(@Valid @RequestBody ValidList<CreateReminderRequest> requests) {
		List<Reminder> reminders = requests.stream().map(MapStructConverter.MAPPER::toReminder).toList();

		log.info("bulk create reminder start..");
		validationService.validateCreateReminder(reminders);
		log.info("bulk validation create finish");

		log.info("getting account information");
		UserDTO user = objectMapper.convertValue(accountFeignClient.getCurrentUserId(), UserDTO.class);

		List<Reminder> results = reminderIntegrationService.create(reminders, user);

		log.info("create reminder success, converting result..");
		return results.stream().map(result -> {
			populateService.populateAbstractReminder(result);
			reminderIntegrationService.sendDashboardActivity(result.getCreatedDate());

			return MapStructConverter.MAPPER.toReminderDTO(result);
		}).toList();
	}

	@PostMapping("integration/check-exist")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public List<ReminderCheckDTO> reminderCheck(@Valid @RequestBody ValidList<ReminderCheckDTO> request) {
		return reminderService.checkExistReminder(request);
	}

	@GetMapping("execution-date/{reminderType}/{entityId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public Instant getExecutionDateForEntity(@PathVariable ReminderType reminderType, @PathVariable String entityId) {
		EntityName entityName = MapStructConverter.MAPPER.getEntityName(reminderType);
		return reminderService.findExecutionForEntity(entityName, entityId);
	}

}
