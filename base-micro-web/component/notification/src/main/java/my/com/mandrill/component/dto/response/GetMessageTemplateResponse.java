package my.com.mandrill.component.dto.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.general.constant.ChannelCodeEnum;
import my.com.mandrill.utilities.general.constant.MessageTemplateCodeEnum;

@Data
@Getter
@Setter
public class GetMessageTemplateResponse {

	private final MessageTemplateCodeEnum code;

	private final ChannelCodeEnum channel;

	private final boolean isMultipart;

	private final boolean html;

	private final String from;

	private final String to;

	private final String bcc;

	private String template;

	private String subject;

}
