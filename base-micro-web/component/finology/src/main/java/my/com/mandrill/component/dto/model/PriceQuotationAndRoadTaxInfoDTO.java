package my.com.mandrill.component.dto.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.domain.PriceQuotationResponseDetail;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PriceQuotationAndRoadTaxInfoDTO {

	private PriceQuotationResponseDetail priceQuotation;

	private PriceQuotationResponseDetail roadTaxInfo;

}
