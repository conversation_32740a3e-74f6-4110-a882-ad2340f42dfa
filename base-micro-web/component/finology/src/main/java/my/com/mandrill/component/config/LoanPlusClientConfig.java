package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.LoanPlusClient;
import my.com.mandrill.component.config.filter.LoanPlusDynamicHeaderFilter;
import my.com.mandrill.component.config.properties.IntegrationLoanPlusProperties;
import my.com.mandrill.utilities.general.util.ConnectionUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.netty.http.client.HttpClient;

@Configuration
@ConditionalOnProperty(name = "integration-loanplus.enabled", havingValue = "true")
@RequiredArgsConstructor
public class LoanPlusClientConfig {

	private final IntegrationLoanPlusProperties integrationLoanPlusProperties;

	@Bean
	public WebClient loanPlusWebClient() {
		return WebClient.builder().baseUrl(integrationLoanPlusProperties.getUrl())
				.clientConnector(new ReactorClientHttpConnector(
						HttpClient.create(ConnectionUtil.getDefaultConnectionProvider())))
				.filter(new LoanPlusDynamicHeaderFilter(integrationLoanPlusProperties)).build();
	}

	@Bean
	public LoanPlusClient loanPlusClient(WebClient loanPlusWebClient) {
		HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
				.builder(WebClientAdapter.forClient(loanPlusWebClient))
				.blockTimeout(integrationLoanPlusProperties.getBlockingTimeout()).build();
		return httpServiceProxyFactory.createClient(LoanPlusClient.class);
	}

}
