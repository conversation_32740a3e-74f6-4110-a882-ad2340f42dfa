package my.com.mandrill.component.controller;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Brand;
import my.com.mandrill.component.domain.VehicleType;
import my.com.mandrill.component.dto.model.BrandDTO;
import my.com.mandrill.component.service.BrandService;
import my.com.mandrill.component.service.VehicleTypeService;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("brands")
public class BrandController {

	private final BrandService brandService;

	private final VehicleTypeService vehicleTypeService;

	public BrandController(BrandService brandService, VehicleTypeService vehicleTypeService) {
		this.brandService = brandService;
		this.vehicleTypeService = vehicleTypeService;
	}

	@GetMapping
	public ResponseEntity<List<BrandDTO>> findAll(Sort sort, @RequestParam String vehicleTypeId) {
		VehicleType vehicleType = vehicleTypeService.findById(vehicleTypeId);
		List<Brand> result = brandService.findByVehicleType(vehicleType, sort);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toBrandDTOList(result));
	}

}
