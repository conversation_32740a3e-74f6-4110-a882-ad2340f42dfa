package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import lombok.*;
import my.com.mandrill.utilities.converter.ProtectedDataConverterBigDecimal;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "retirement_account", uniqueConstraints = @UniqueConstraint(
		columnNames = { "user_id", "retirement_account_type_id", "retirement_provider_id" }))
public class RetirementAccount extends AuditSection {

	@Column(name = "user_id", length = 36)
	private String userId;

	@Column(name = "savings_amount", length = 100)
	@Convert(converter = ProtectedDataConverterBigDecimal.class)
	private BigDecimal savingsAmount;

	@ManyToOne
	@JoinColumn(name = "retirement_account_type_id")
	private RetirementAccountType accountType;

	@ManyToOne
	@JoinColumn(name = "retirement_provider_id")
	private RetirementProvider retirementProvider;

	@Column(name = "monthly_income", length = 100)
	@Convert(converter = ProtectedDataConverterBigDecimal.class)
	private BigDecimal monthlyIncome;

	@Column(name = "employee_contribution_percentage")
	private BigDecimal employeeContributionPercentage;

	@Column(name = "employer_contribution_percentage")
	private BigDecimal employerContributionPercentage;

	@Column(name = "monthly_contribution", length = 100)
	@Convert(converter = ProtectedDataConverterBigDecimal.class)
	private BigDecimal monthlyContribution;

	@Column(name = "latest_contribution_date")
	private LocalDate latestContributionDate;

	// below field is to keep track the latest saving calculation date
	@Column(name = "latest_saving_calculation_date")
	private LocalDate latestSavingCalculationDate;

	@Column(name = "attachment_group_id", length = 36)
	private String attachmentGroupId;

}
