package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.io.Serializable;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "favourite_unit", indexes = @Index(name = "idx_favourite_unit_favourite_id", columnList = "favourite_id"))
public class FavouriteUnit implements Serializable {

	@Id
	@GeneratedValue(generator = "uuid")
	@GenericGenerator(name = "uuid", strategy = "uuid2")
	@Column(updatable = false, nullable = false, length = 36)
	private String id;

	@NotNull
	@JsonIgnore
	@ToString.Exclude
	@ManyToOne(optional = false)
	@JoinColumn(name = "favourite_id", nullable = false)
	private Favourite favourite;

	@NotBlank
	@Size(max = 200)
	@Column(name = "unit", length = 200, nullable = false, updatable = false)
	private String unit;

}
