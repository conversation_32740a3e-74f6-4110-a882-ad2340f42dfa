package my.com.mandrill.component.controller.clientIntegration;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.ErrorCodeEnum;
import my.com.mandrill.component.domain.Access;
import my.com.mandrill.component.dto.request.VisitorDataUpdate;
import my.com.mandrill.component.service.AccessService;
import my.com.mandrill.component.service.EAccessIntegrationService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.DateConvertUtil;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/callback/e-access")
@RequiredArgsConstructor
public class PublicApiController {

	private final AccessService accessService;

	private final EAccessIntegrationService eAccessIntegrationService;

	private final ValidationService validationService;

	@PutMapping("/visitor")
	@ResponseStatus(HttpStatus.OK)
	public void updateAccessDetails(@RequestBody VisitorDataUpdate data) throws JsonProcessingException {
		validationService.validateVisitorDataUpdate(data);

		Optional<Access> access = accessService.findByVisitorId(data.getVisitorId());
		if (access.isPresent()) {
			log.info("update coming for visitor id {}", data.getVisitorId());

			Access updatingAccess = access.get();

			updatingAccess.setBuildingName(data.getSiteName());
			updatingAccess.setBuildingCode(data.getSiteCode());
			updatingAccess.setTenantName(data.getTenantName());
			updatingAccess.setTenantCode(data.getTenantCode());
			updatingAccess.setUnits(MapStructConverter.MAPPER.constructAccessUnits(updatingAccess, data.getUnits()));
			updatingAccess.setVisitDatetimeStart(
					DateConvertUtil.toInstant(data.getDateStart(), DateConvertUtil.DATETIME_FORMAT_6, true));
			updatingAccess.setVisitDatetimeEnd(
					DateConvertUtil.toInstant(data.getDateEnd(), DateConvertUtil.DATETIME_FORMAT_6, true));

			Access result = accessService.save(updatingAccess);
			eAccessIntegrationService.publishReminder(result);
		}
		else {
			throw new BusinessException(ErrorCodeEnum.NOT_FOUND);
		}
	}

}
